import { useState, useEffect, useCallback } from 'react';
import { AuthManager, AuthState } from '../../shared/services/AuthManager';
import { SmartEduClient } from '../../shared/services/SmartEduClient';
import { 
  LoginCredentials, 
  UserInfo, 
  CaptchaInfo,
  AuthResult 
} from '../../shared/types';

/**
 * 认证状态接口
 */
export interface AuthHookState {
  authState: AuthState;
  user: UserInfo | null;
  isLoggedIn: boolean;
  isGuestMode: boolean;
  isLoading: boolean;
  error: string | null;
  captchaRequired: boolean;
  captchaImage: string | null;
}

/**
 * 认证操作接口
 */
export interface AuthHookActions {
  login: (credentials: LoginCredentials) => Promise<AuthResult>;
  loginAsGuest: () => Promise<void>;
  logout: () => Promise<void>;
  refreshCaptcha: () => Promise<void>;
  clearError: () => void;
  validateSession: () => Promise<boolean>;
}

/**
 * useAuth Hook
 * 提供认证状态管理和操作方法
 */
export const useAuth = (): AuthHookState & AuthHookActions => {
  // 状态管理
  const [authState, setAuthState] = useState<AuthState>(AuthState.GUEST);
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [captchaRequired, setCaptchaRequired] = useState(false);
  const [captchaImage, setCaptchaImage] = useState<string | null>(null);

  // 服务实例（在实际应用中应该通过依赖注入或上下文提供）
  const [authManager] = useState(() => {
    const client = new SmartEduClient();
    return new AuthManager(client);
  });

  // 计算派生状态
  const isLoggedIn = authState === AuthState.LOGGED_IN;
  const isGuestMode = authState === AuthState.GUEST;

  // 设置事件监听器
  useEffect(() => {
    const handleStateChange = (state: AuthState, userData?: UserInfo) => {
      setAuthState(state);
      setUser(userData || null);
      
      // 根据状态更新loading状态
      if (state === AuthState.LOGGING_IN) {
        setIsLoading(true);
      } else {
        setIsLoading(false);
      }
    };

    const handleLoginSuccess = (userData: UserInfo) => {
      setUser(userData);
      setError(null);
      setCaptchaRequired(false);
      setCaptchaImage(null);
      setIsLoading(false);
    };

    const handleLoginError = (errorMessage: string) => {
      setError(errorMessage);
      setIsLoading(false);
    };

    const handleLogout = () => {
      setUser(null);
      setError(null);
      setCaptchaRequired(false);
      setCaptchaImage(null);
      setIsLoading(false);
    };

    const handleSessionExpired = () => {
      setUser(null);
      setError('会话已过期，请重新登录');
      setCaptchaRequired(false);
      setCaptchaImage(null);
      setIsLoading(false);
    };

    const handleCaptchaRequired = (captchaInfo: CaptchaInfo) => {
      setCaptchaRequired(true);
      setCaptchaImage(captchaInfo.image);
      setIsLoading(false);
    };

    // 注册事件监听器
    authManager.on('stateChange', handleStateChange);
    authManager.on('loginSuccess', handleLoginSuccess);
    authManager.on('loginError', handleLoginError);
    authManager.on('logout', handleLogout);
    authManager.on('sessionExpired', handleSessionExpired);
    authManager.on('captchaRequired', handleCaptchaRequired);

    // 初始化状态
    setAuthState(authManager.getCurrentState());
    setUser(authManager.getCurrentUser());

    // 清理函数
    return () => {
      authManager.removeAllListeners();
    };
  }, [authManager]);

  // 登录操作
  const login = useCallback(async (credentials: LoginCredentials): Promise<AuthResult> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await authManager.loginWithCredentials(
        credentials.username,
        credentials.password,
        credentials.captcha
      );

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录失败';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [authManager]);

  // 访客模式登录
  const loginAsGuest = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      
      await authManager.loginAsGuest();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '切换到访客模式失败';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [authManager]);

  // 登出操作
  const logout = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      await authManager.logout();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登出失败';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [authManager]);

  // 刷新验证码
  const refreshCaptcha = useCallback(async (): Promise<void> => {
    try {
      const captchaInfo = await authManager.getCaptcha();
      setCaptchaImage(captchaInfo.image);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取验证码失败';
      setError(errorMessage);
    }
  }, [authManager]);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 验证会话
  const validateSession = useCallback(async (): Promise<boolean> => {
    try {
      return await authManager.validateSession();
    } catch (error) {
      console.error('验证会话失败:', error);
      return false;
    }
  }, [authManager]);

  return {
    // 状态
    authState,
    user,
    isLoggedIn,
    isGuestMode,
    isLoading,
    error,
    captchaRequired,
    captchaImage,
    
    // 操作
    login,
    loginAsGuest,
    logout,
    refreshCaptcha,
    clearError,
    validateSession
  };
};

export default useAuth;