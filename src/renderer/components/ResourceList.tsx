import React, { useState, useCallback, useMemo } from 'react';
import { 
  Card, 
  List, 
  Button, 
  Space, 
  Tag, 
  Typography, 
  Checkbox, 
  Alert, 
  Tooltip, 
  Badge,
  Empty,
  Spin,
  Row,
  Col,
  Switch,
  Divider
} from 'antd';
import { 
  DownloadOutlined, 
  EyeOutlined, 
  LockOutlined, 
  UnlockOutlined,
  BookOutlined,
  PlayCircleOutlined,
  SelectOutlined,
  AppstoreOutlined,
  BarsOutlined
} from '@ant-design/icons';
import { 
  CourseResource, 
  ResourceListProps,
  ResourceDetail 
} from '../../shared/types';

const { Text, Title, Paragraph } = Typography;

type ViewMode = 'card' | 'list';

/**
 * 资源列表组件
 * 支持卡片和列表两种展示模式，提供资源下载和批量操作功能
 */
export const ResourceList: React.FC<ResourceListProps> = ({
  resources,
  onDownload,
  onBatchDownload,
  userLoggedIn,
  loading = false,
  selectedResources = [],
  onSelectionChange
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('card');
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedResources);
  const [expandedDetails, setExpandedDetails] = useState<Record<string, ResourceDetail>>({});
  const [loadingDetails, setLoadingDetails] = useState<Record<string, boolean>>({});

  /**
   * 处理资源选择变更
   */
  const handleSelectionChange = useCallback((resourceId: string, checked: boolean) => {
    const newSelectedIds = checked 
      ? [...selectedIds, resourceId]
      : selectedIds.filter(id => id !== resourceId);
    
    setSelectedIds(newSelectedIds);
    onSelectionChange?.(newSelectedIds);
  }, [selectedIds, onSelectionChange]);

  /**
   * 处理全选/取消全选
   */
  const handleSelectAll = useCallback((checked: boolean) => {
    const newSelectedIds = checked ? resources.map(r => r.id) : [];
    setSelectedIds(newSelectedIds);
    onSelectionChange?.(newSelectedIds);
  }, [resources, onSelectionChange]);

  /**
   * 处理单个资源下载
   */
  const handleDownload = useCallback((resource: CourseResource) => {
    onDownload(resource);
  }, [onDownload]);

  /**
   * 处理批量下载
   */
  const handleBatchDownload = useCallback(() => {
    const selectedResources = resources.filter(r => selectedIds.includes(r.id));
    onBatchDownload(selectedResources);
  }, [resources, selectedIds, onBatchDownload]);

  /**
   * 获取资源类型图标
   */
  const getResourceIcon = useCallback((type: string) => {
    switch (type) {
      case 'textbook':
        return <BookOutlined />;
      case 'video':
        return <PlayCircleOutlined />;
      default:
        return <BookOutlined />;
    }
  }, []);

  /**
   * 获取访问级别标签
   */
  const getAccessLevelTag = useCallback((resource: CourseResource) => {
    const { accessLevel, requiresAuth } = resource;
    
    if (!requiresAuth) {
      return <Tag color="green" icon={<UnlockOutlined />}>公开</Tag>;
    }
    
    switch (accessLevel) {
      case 'public':
        return <Tag color="green" icon={<UnlockOutlined />}>公开</Tag>;
      case 'registered':
        return <Tag color="blue" icon={<LockOutlined />}>需登录</Tag>;
      case 'premium':
        return <Tag color="gold" icon={<LockOutlined />}>高级</Tag>;
      default:
        return <Tag color="default" icon={<LockOutlined />}>未知</Tag>;
    }
  }, []);

  /**
   * 检查资源是否可下载
   */
  const isResourceDownloadable = useCallback((resource: CourseResource) => {
    if (!resource.requiresAuth) {
      return true;
    }
    
    if (userLoggedIn) {
      return resource.accessLevel !== 'premium';
    }
    
    return false;
  }, [userLoggedIn]);

  /**
   * 获取下载按钮状态和提示
   */
  const getDownloadButtonProps = useCallback((resource: CourseResource) => {
    const downloadable = isResourceDownloadable(resource);
    
    if (!downloadable) {
      if (!userLoggedIn && resource.requiresAuth) {
        return {
          disabled: true,
          tooltip: '请先登录以下载此资源'
        };
      }
      
      if (resource.accessLevel === 'premium') {
        return {
          disabled: true,
          tooltip: '此资源需要高级权限'
        };
      }
    }
    
    return {
      disabled: false,
      tooltip: undefined
    };
  }, [userLoggedIn, isResourceDownloadable]);

  /**
   * 格式化文件大小
   */
  const formatFileSize = useCallback((bytes?: number) => {
    if (!bytes) return '未知';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }, []);

  /**
   * 格式化时长
   */
  const formatDuration = useCallback((seconds?: number) => {
    if (!seconds) return '未知';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  /**
   * 渲染资源元数据
   */
  const renderResourceMetadata = useCallback((resource: CourseResource) => {
    const { metadata } = resource;
    
    return (
      <Space wrap>
        <Tag>{metadata.stage}</Tag>
        <Tag>{metadata.grade}</Tag>
        <Tag>{metadata.subject}</Tag>
        <Tag>{metadata.version}</Tag>
        <Tag>{metadata.volume}</Tag>
        {metadata.chapter && <Tag color="blue">{metadata.chapter}</Tag>}
        {metadata.lesson && <Tag color="green">{metadata.lesson}</Tag>}
      </Space>
    );
  }, []);

  /**
   * 渲染资源统计信息
   */
  const renderResourceStats = useCallback((resource: CourseResource) => {
    const { type, metadata } = resource;
    
    return (
      <Space split={<Divider type="vertical" />}>
        {type === 'textbook' && metadata.fileSize && (
          <Text type="secondary">大小: {formatFileSize(metadata.fileSize)}</Text>
        )}
        {type === 'video' && metadata.duration && (
          <Text type="secondary">时长: {formatDuration(metadata.duration)}</Text>
        )}
        {type === 'video' && metadata.fileSize && (
          <Text type="secondary">大小: {formatFileSize(metadata.fileSize)}</Text>
        )}
      </Space>
    );
  }, [formatFileSize, formatDuration]);

  /**
   * 渲染卡片模式的资源项
   */
  const renderCardItem = useCallback((resource: CourseResource) => {
    const downloadButtonProps = getDownloadButtonProps(resource);
    const isSelected = selectedIds.includes(resource.id);
    
    return (
      <Card
        key={resource.id}
        size="small"
        hoverable
        className={isSelected ? 'selected-resource-card' : ''}
        title={
          <Space>
            <Checkbox
              checked={isSelected}
              onChange={(e) => handleSelectionChange(resource.id, e.target.checked)}
            />
            {getResourceIcon(resource.type)}
            <Text strong>{resource.title}</Text>
          </Space>
        }
        extra={getAccessLevelTag(resource)}
        actions={[
          <Tooltip title={downloadButtonProps.tooltip} key="download">
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              disabled={downloadButtonProps.disabled}
              onClick={() => handleDownload(resource)}
              size="small"
            >
              下载
            </Button>
          </Tooltip>,
          <Button
            icon={<EyeOutlined />}
            onClick={() => {/* TODO: 查看详情 */}}
            size="small"
            key="detail"
          >
            详情
          </Button>
        ]}
      >
        <div style={{ marginBottom: 8 }}>
          {renderResourceMetadata(resource)}
        </div>
        <div>
          {renderResourceStats(resource)}
        </div>
      </Card>
    );
  }, [selectedIds, getDownloadButtonProps, getResourceIcon, getAccessLevelTag, handleSelectionChange, handleDownload, renderResourceMetadata, renderResourceStats]);

  /**
   * 渲染列表模式的资源项
   */
  const renderListItem = useCallback((resource: CourseResource) => {
    const downloadButtonProps = getDownloadButtonProps(resource);
    const isSelected = selectedIds.includes(resource.id);
    
    return (
      <List.Item
        key={resource.id}
        className={isSelected ? 'selected-resource-item' : ''}
        actions={[
          <Tooltip title={downloadButtonProps.tooltip} key="download">
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              disabled={downloadButtonProps.disabled}
              onClick={() => handleDownload(resource)}
              size="small"
            >
              下载
            </Button>
          </Tooltip>,
          <Button
            icon={<EyeOutlined />}
            onClick={() => {/* TODO: 查看详情 */}}
            size="small"
            key="detail"
          >
            详情
          </Button>
        ]}
      >
        <List.Item.Meta
          avatar={
            <Checkbox
              checked={isSelected}
              onChange={(e) => handleSelectionChange(resource.id, e.target.checked)}
            />
          }
          title={
            <Space>
              {getResourceIcon(resource.type)}
              <Text strong>{resource.title}</Text>
              {getAccessLevelTag(resource)}
            </Space>
          }
          description={
            <div>
              <div style={{ marginBottom: 4 }}>
                {renderResourceMetadata(resource)}
              </div>
              <div>
                {renderResourceStats(resource)}
              </div>
            </div>
          }
        />
      </List.Item>
    );
  }, [selectedIds, getDownloadButtonProps, getResourceIcon, getAccessLevelTag, handleSelectionChange, handleDownload, renderResourceMetadata, renderResourceStats]);

  /**
   * 计算统计信息
   */
  const stats = useMemo(() => {
    const total = resources.length;
    const selected = selectedIds.length;
    const downloadable = resources.filter(r => isResourceDownloadable(r)).length;
    const textbooks = resources.filter(r => r.type === 'textbook').length;
    const videos = resources.filter(r => r.type === 'video').length;
    
    return { total, selected, downloadable, textbooks, videos };
  }, [resources, selectedIds, isResourceDownloadable]);

  /**
   * 检查是否全选
   */
  const isAllSelected = useMemo(() => {
    return resources.length > 0 && selectedIds.length === resources.length;
  }, [resources.length, selectedIds.length]);

  /**
   * 检查是否部分选择
   */
  const isIndeterminate = useMemo(() => {
    return selectedIds.length > 0 && selectedIds.length < resources.length;
  }, [selectedIds.length, resources.length]);

  if (loading) {
    return (
      <Card>
        <Spin size="large" style={{ display: 'block', textAlign: 'center', padding: '50px 0' }} />
      </Card>
    );
  }

  if (resources.length === 0) {
    return (
      <Card>
        <Empty 
          description="暂无资源"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <Card
      title={
        <Space>
          <Title level={4} style={{ margin: 0 }}>资源列表</Title>
          <Badge count={stats.total} showZero color="blue" />
        </Space>
      }
      extra={
        <Space>
          <Switch
            checkedChildren={<AppstoreOutlined />}
            unCheckedChildren={<BarsOutlined />}
            checked={viewMode === 'card'}
            onChange={(checked) => setViewMode(checked ? 'card' : 'list')}
          />
        </Space>
      }
    >
      {/* 统计信息和批量操作 */}
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Space wrap>
              <Checkbox
                indeterminate={isIndeterminate}
                checked={isAllSelected}
                onChange={(e) => handleSelectAll(e.target.checked)}
              >
                全选
              </Checkbox>
              <Text type="secondary">
                共 {stats.total} 个资源，已选择 {stats.selected} 个
              </Text>
              <Text type="secondary">
                可下载 {stats.downloadable} 个
              </Text>
              <Text type="secondary">
                教材 {stats.textbooks} 个，视频 {stats.videos} 个
              </Text>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              disabled={selectedIds.length === 0}
              onClick={handleBatchDownload}
            >
              批量下载 ({selectedIds.length})
            </Button>
          </Col>
        </Row>
      </div>

      {/* 权限提示 */}
      {!userLoggedIn && resources.some(r => r.requiresAuth) && (
        <Alert
          message="部分资源需要登录后才能下载"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 资源列表 */}
      {viewMode === 'card' ? (
        <Row gutter={[16, 16]}>
          {resources.map(resource => (
            <Col key={resource.id} xs={24} sm={12} md={8} lg={6}>
              {renderCardItem(resource)}
            </Col>
          ))}
        </Row>
      ) : (
        <List
          dataSource={resources}
          renderItem={renderListItem}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
        />
      )}
    </Card>
  );
};

export default ResourceList;