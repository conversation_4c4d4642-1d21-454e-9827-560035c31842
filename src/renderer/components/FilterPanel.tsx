import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Select, Form, Row, Col, Button, Space, Alert, Spin, Card } from 'antd';
import { ReloadOutlined, ClearOutlined } from '@ant-design/icons';
import { 
  CourseFilters, 
  FilterOption, 
  FilterPanelProps,
  ApiError 
} from '../../shared/types';
import { FilterService } from '../../shared/services/FilterService';
import { SmartEduClient } from '../../shared/services/SmartEduClient';

const { Option } = Select;

/**
 * 筛选面板组件
 * 提供课程资源的级联筛选功能
 */
export const FilterPanel: React.FC<FilterPanelProps> = ({
  onFilterChange,
  loading = false,
  initialFilters = {},
  disabled = false
}) => {
  const [form] = Form.useForm();
  const [filters, setFilters] = useState<Partial<CourseFilters>>(initialFilters);
  const [options, setOptions] = useState<{
    stages: FilterOption[];
    grades: FilterOption[];
    subjects: FilterOption[];
    versions: FilterOption[];
    volumes: FilterOption[];
  }>({
    stages: [],
    grades: [],
    subjects: [],
    versions: [],
    volumes: []
  });
  
  const [loadingStates, setLoadingStates] = useState({
    stages: false,
    grades: false,
    subjects: false,
    versions: false,
    volumes: false
  });
  
  const [error, setError] = useState<string | null>(null);

  // 创建服务实例
  const filterService = useMemo(() => {
    const client = new SmartEduClient();
    return new FilterService(client);
  }, []);

  /**
   * 设置加载状态
   */
  const setLoadingState = useCallback((field: keyof typeof loadingStates, loading: boolean) => {
    setLoadingStates(prev => ({ ...prev, [field]: loading }));
  }, []);

  /**
   * 处理错误
   */
  const handleError = useCallback((error: Error, context: string) => {
    console.error(`${context}失败:`, error);
    setError(`${context}失败: ${error.message}`);
  }, []);

  /**
   * 清除下级选项
   */
  const clearSubsequentOptions = useCallback((level: string) => {
    const levels = ['stage', 'grade', 'subject', 'version', 'volume'];
    const currentIndex = levels.indexOf(level);
    
    if (currentIndex >= 0) {
      const fieldsToReset = levels.slice(currentIndex + 1);
      const newFilters = { ...filters };
      const newOptions = { ...options };
      
      fieldsToReset.forEach(field => {
        delete newFilters[field as keyof CourseFilters];
        newOptions[`${field}s` as keyof typeof options] = [];
      });
      
      setFilters(newFilters);
      setOptions(newOptions);
      
      // 重置表单字段
      const formValues = form.getFieldsValue();
      fieldsToReset.forEach(field => {
        delete formValues[field];
      });
      form.setFieldsValue(formValues);
    }
  }, [filters, options, form]);

  /**
   * 加载学段列表
   */
  const loadStages = useCallback(async () => {
    setLoadingState('stages', true);
    setError(null);
    
    try {
      const stages = await filterService.getStages();
      setOptions(prev => ({ ...prev, stages }));
    } catch (error) {
      handleError(error as Error, '加载学段列表');
    } finally {
      setLoadingState('stages', false);
    }
  }, [filterService, setLoadingState, handleError]);

  /**
   * 加载年级列表
   */
  const loadGrades = useCallback(async (stage: string) => {
    if (!stage) return;
    
    setLoadingState('grades', true);
    
    try {
      const grades = await filterService.getGradesByStage(stage);
      setOptions(prev => ({ ...prev, grades }));
    } catch (error) {
      handleError(error as Error, '加载年级列表');
    } finally {
      setLoadingState('grades', false);
    }
  }, [filterService, setLoadingState, handleError]);

  /**
   * 加载学科列表
   */
  const loadSubjects = useCallback(async (stage: string, grade: string) => {
    if (!stage || !grade) return;
    
    setLoadingState('subjects', true);
    
    try {
      const subjects = await filterService.getSubjectsByStageAndGrade(stage, grade);
      setOptions(prev => ({ ...prev, subjects }));
    } catch (error) {
      handleError(error as Error, '加载学科列表');
    } finally {
      setLoadingState('subjects', false);
    }
  }, [filterService, setLoadingState, handleError]);

  /**
   * 加载版本列表
   */
  const loadVersions = useCallback(async (stage: string, grade: string, subject: string) => {
    if (!stage || !grade || !subject) return;
    
    setLoadingState('versions', true);
    
    try {
      const versions = await filterService.getVersionsByStageGradeSubject(stage, grade, subject);
      setOptions(prev => ({ ...prev, versions }));
    } catch (error) {
      handleError(error as Error, '加载版本列表');
    } finally {
      setLoadingState('versions', false);
    }
  }, [filterService, setLoadingState, handleError]);

  /**
   * 加载册次列表
   */
  const loadVolumes = useCallback(async (stage: string, grade: string, subject: string, version: string) => {
    if (!stage || !grade || !subject || !version) return;
    
    setLoadingState('volumes', true);
    
    try {
      const volumes = await filterService.getVolumesByStageGradeSubjectVersion(
        stage, grade, subject, version
      );
      setOptions(prev => ({ ...prev, volumes }));
    } catch (error) {
      handleError(error as Error, '加载册次列表');
    } finally {
      setLoadingState('volumes', false);
    }
  }, [filterService, setLoadingState, handleError]);

  /**
   * 处理筛选条件变更
   */
  const handleFilterChange = useCallback((field: keyof CourseFilters, value: string) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
    
    // 清除下级选项
    clearSubsequentOptions(field);
    
    // 根据变更的字段加载下级选项
    switch (field) {
      case 'stage':
        if (value) {
          loadGrades(value);
        }
        break;
      case 'grade':
        if (value && newFilters.stage) {
          loadSubjects(newFilters.stage, value);
        }
        break;
      case 'subject':
        if (value && newFilters.stage && newFilters.grade) {
          loadVersions(newFilters.stage, newFilters.grade, value);
        }
        break;
      case 'version':
        if (value && newFilters.stage && newFilters.grade && newFilters.subject) {
          loadVolumes(newFilters.stage, newFilters.grade, newFilters.subject, value);
        }
        break;
    }
    
    // 通知父组件筛选条件变更
    onFilterChange(newFilters as CourseFilters);
  }, [filters, clearSubsequentOptions, loadGrades, loadSubjects, loadVersions, loadVolumes, onFilterChange]);

  /**
   * 重置筛选条件
   */
  const handleReset = useCallback(() => {
    form.resetFields();
    setFilters({});
    setOptions({
      stages: options.stages, // 保留学段选项
      grades: [],
      subjects: [],
      versions: [],
      volumes: []
    });
    setError(null);
    onFilterChange({} as CourseFilters);
  }, [form, options.stages, onFilterChange]);

  /**
   * 刷新当前级别的选项
   */
  const handleRefresh = useCallback(() => {
    setError(null);
    loadStages();
    
    // 重新加载当前已选择的级联选项
    if (filters.stage) {
      loadGrades(filters.stage);
      
      if (filters.grade) {
        loadSubjects(filters.stage, filters.grade);
        
        if (filters.subject) {
          loadVersions(filters.stage, filters.grade, filters.subject);
          
          if (filters.version) {
            loadVolumes(filters.stage, filters.grade, filters.subject, filters.version);
          }
        }
      }
    }
  }, [filters, loadStages, loadGrades, loadSubjects, loadVersions, loadVolumes]);

  /**
   * 初始化加载
   */
  useEffect(() => {
    loadStages();
    
    // 如果有初始筛选条件，加载对应的选项
    if (initialFilters.stage) {
      loadGrades(initialFilters.stage);
      
      if (initialFilters.grade) {
        loadSubjects(initialFilters.stage, initialFilters.grade);
        
        if (initialFilters.subject) {
          loadVersions(initialFilters.stage, initialFilters.grade, initialFilters.subject);
          
          if (initialFilters.version) {
            loadVolumes(
              initialFilters.stage, 
              initialFilters.grade, 
              initialFilters.subject, 
              initialFilters.version
            );
          }
        }
      }
    }
  }, []);

  /**
   * 同步表单值
   */
  useEffect(() => {
    form.setFieldsValue(filters);
  }, [form, filters]);

  /**
   * 检查筛选条件是否完整
   */
  const isFiltersComplete = useMemo(() => {
    return !!(filters.stage && filters.grade && filters.subject && filters.version && filters.volume);
  }, [filters]);

  /**
   * 检查是否有任何加载状态
   */
  const isAnyLoading = useMemo(() => {
    return Object.values(loadingStates).some(loading => loading);
  }, [loadingStates]);

  return (
    <Card 
      title="课程筛选" 
      size="small"
      extra={
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            disabled={disabled || loading}
            size="small"
          >
            刷新
          </Button>
          <Button 
            icon={<ClearOutlined />} 
            onClick={handleReset}
            disabled={disabled || loading}
            size="small"
          >
            重置
          </Button>
        </Space>
      }
    >
      {error && (
        <Alert 
          message={error} 
          type="error" 
          closable 
          onClose={() => setError(null)}
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Spin spinning={loading || isAnyLoading}>
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={24} md={12} lg={8}>
              <Form.Item label="学段" name="stage">
                <Select
                  placeholder="请选择学段"
                  onChange={(value) => handleFilterChange('stage', value)}
                  disabled={disabled || loadingStates.stages}
                  loading={loadingStates.stages}
                  allowClear
                >
                  {options.stages.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={24} md={12} lg={8}>
              <Form.Item label="年级" name="grade">
                <Select
                  placeholder="请选择年级"
                  onChange={(value) => handleFilterChange('grade', value)}
                  disabled={disabled || !filters.stage || loadingStates.grades}
                  loading={loadingStates.grades}
                  allowClear
                >
                  {options.grades.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={24} md={12} lg={8}>
              <Form.Item label="学科" name="subject">
                <Select
                  placeholder="请选择学科"
                  onChange={(value) => handleFilterChange('subject', value)}
                  disabled={disabled || !filters.grade || loadingStates.subjects}
                  loading={loadingStates.subjects}
                  allowClear
                >
                  {options.subjects.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={24} md={12} lg={8}>
              <Form.Item label="版本" name="version">
                <Select
                  placeholder="请选择版本"
                  onChange={(value) => handleFilterChange('version', value)}
                  disabled={disabled || !filters.subject || loadingStates.versions}
                  loading={loadingStates.versions}
                  allowClear
                >
                  {options.versions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={24} md={12} lg={8}>
              <Form.Item label="册次" name="volume">
                <Select
                  placeholder="请选择册次"
                  onChange={(value) => handleFilterChange('volume', value)}
                  disabled={disabled || !filters.version || loadingStates.volumes}
                  loading={loadingStates.volumes}
                  allowClear
                >
                  {options.volumes.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Spin>
      
      {isFiltersComplete && (
        <Alert 
          message="筛选条件已完整，可以开始搜索资源" 
          type="success" 
          showIcon 
          style={{ marginTop: 16 }}
        />
      )}
    </Card>
  );
};

export default FilterPanel;