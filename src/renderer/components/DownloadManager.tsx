import React, { useState, useMemo } from 'react';
import { 
  Card, 
  <PERSON>, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  Empty, 
  Statistic, 
  Row, 
  Col,
  Switch,
  Tooltip,
  Badge
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ClearOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { DownloadTask, TaskStatus, DownloadManagerProps } from '../../shared/types';
import { DownloadProgress } from './DownloadProgress';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 扩展的下载管理器属性
 */
interface ExtendedDownloadManagerProps extends DownloadManagerProps {
  onPauseAll?: () => void;
  onResumeAll?: () => void;
  onCancelAll?: () => void;
  onClearCompleted?: () => void;
  stats?: {
    totalTasks: number;
    completedTasks: number;
    failedTasks: number;
    activeTasks: number;
    totalDownloadedBytes: number;
    averageSpeed: number;
  };
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化速度
 */
const formatSpeed = (bytesPerSecond: number): string => {
  return `${formatFileSize(bytesPerSecond)}/s`;
};

/**
 * 下载管理器组件
 */
export const DownloadManager: React.FC<ExtendedDownloadManagerProps> = ({
  tasks,
  onPause,
  onResume,
  onCancel,
  onRetry,
  onClear,
  onPauseAll,
  onResumeAll,
  onCancelAll,
  onClearCompleted,
  showCompleted = true,
  stats
}) => {
  const [compactMode, setCompactMode] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  // 按状态分组任务
  const tasksByStatus = useMemo(() => {
    const groups: Record<string, DownloadTask[]> = {
      all: tasks,
      downloading: tasks.filter(t => t.status === 'downloading'),
      pending: tasks.filter(t => t.status === 'pending'),
      paused: tasks.filter(t => t.status === 'paused'),
      completed: tasks.filter(t => t.status === 'completed'),
      failed: tasks.filter(t => t.status === 'failed'),
      cancelled: tasks.filter(t => t.status === 'cancelled')
    };
    
    return groups;
  }, [tasks]);

  // 计算统计信息
  const calculatedStats = useMemo(() => {
    if (stats) return stats;
    
    const activeTasks = tasksByStatus.downloading;
    const totalDownloadedBytes = tasksByStatus.completed.reduce((sum, task) => {
      return sum + (task.resource.metadata?.fileSize || 0);
    }, 0);
    const averageSpeed = activeTasks.length > 0 
      ? activeTasks.reduce((sum, task) => sum + task.speed, 0) / activeTasks.length
      : 0;

    return {
      totalTasks: tasks.length,
      completedTasks: tasksByStatus.completed.length,
      failedTasks: tasksByStatus.failed.length,
      activeTasks: tasksByStatus.downloading.length,
      totalDownloadedBytes,
      averageSpeed
    };
  }, [tasks, tasksByStatus, stats]);

  // 渲染统计卡片
  const renderStatsCards = () => (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card size="small">
          <Statistic
            title="总任务"
            value={calculatedStats.totalTasks}
            prefix={<DownloadOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card size="small">
          <Statistic
            title="进行中"
            value={calculatedStats.activeTasks}
            prefix={<PlayCircleOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card size="small">
          <Statistic
            title="已完成"
            value={calculatedStats.completedTasks}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card size="small">
          <Statistic
            title="失败"
            value={calculatedStats.failedTasks}
            prefix={<ExclamationCircleOutlined />}
            valueStyle={{ color: '#ff4d4f' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      marginBottom: 16,
      padding: '12px 16px',
      background: '#fafafa',
      borderRadius: 6
    }}>
      <Space>
        <Text strong>批量操作:</Text>
        {onPauseAll && (
          <Tooltip title="暂停所有下载">
            <Button 
              size="small" 
              icon={<PauseCircleOutlined />}
              onClick={onPauseAll}
              disabled={tasksByStatus.downloading.length === 0}
            >
              暂停全部
            </Button>
          </Tooltip>
        )}
        {onResumeAll && (
          <Tooltip title="恢复所有暂停的下载">
            <Button 
              size="small" 
              icon={<PlayCircleOutlined />}
              onClick={onResumeAll}
              disabled={tasksByStatus.paused.length === 0}
            >
              恢复全部
            </Button>
          </Tooltip>
        )}
        {onCancelAll && (
          <Tooltip title="取消所有下载">
            <Button 
              size="small" 
              icon={<StopOutlined />}
              onClick={onCancelAll}
              disabled={tasksByStatus.downloading.length === 0 && tasksByStatus.pending.length === 0}
              danger
            >
              取消全部
            </Button>
          </Tooltip>
        )}
        {onClearCompleted && (
          <Tooltip title="清除已完成的任务">
            <Button 
              size="small" 
              icon={<ClearOutlined />}
              onClick={onClearCompleted}
              disabled={tasksByStatus.completed.length === 0}
            >
              清除已完成
            </Button>
          </Tooltip>
        )}
      </Space>

      <Space>
        {calculatedStats.averageSpeed > 0 && (
          <Text type="secondary">
            平均速度: {formatSpeed(calculatedStats.averageSpeed)}
          </Text>
        )}
        <Text type="secondary">紧凑模式:</Text>
        <Switch 
          size="small"
          checked={compactMode}
          onChange={setCompactMode}
        />
      </Space>
    </div>
  );

  // 渲染任务列表
  const renderTaskList = (taskList: DownloadTask[]) => {
    if (taskList.length === 0) {
      return (
        <Empty 
          description="暂无下载任务"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <List
        dataSource={taskList}
        renderItem={(task) => (
          <List.Item key={task.id} style={{ padding: compactMode ? 0 : undefined }}>
            <DownloadProgress
              task={task}
              onPause={onPause}
              onResume={onResume}
              onCancel={onCancel}
              onRetry={onRetry}
              onClear={onClear}
              compact={compactMode}
            />
          </List.Item>
        )}
      />
    );
  };

  // 获取标签页标题和徽章
  const getTabTitle = (key: string, count: number) => {
    const titles: Record<string, string> = {
      all: '全部',
      downloading: '下载中',
      pending: '等待中',
      paused: '已暂停',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消'
    };

    return (
      <span>
        {titles[key]}
        {count > 0 && <Badge count={count} style={{ marginLeft: 8 }} />}
      </span>
    );
  };

  return (
    <div>
      <Title level={4} style={{ marginBottom: 16 }}>
        下载管理器
      </Title>

      {renderStatsCards()}
      {renderToolbar()}

      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          type="card"
          size="small"
        >
          <TabPane 
            tab={getTabTitle('all', tasksByStatus.all.length)} 
            key="all"
          >
            {renderTaskList(tasksByStatus.all)}
          </TabPane>
          
          <TabPane 
            tab={getTabTitle('downloading', tasksByStatus.downloading.length)} 
            key="downloading"
          >
            {renderTaskList(tasksByStatus.downloading)}
          </TabPane>
          
          <TabPane 
            tab={getTabTitle('pending', tasksByStatus.pending.length)} 
            key="pending"
          >
            {renderTaskList(tasksByStatus.pending)}
          </TabPane>
          
          <TabPane 
            tab={getTabTitle('paused', tasksByStatus.paused.length)} 
            key="paused"
          >
            {renderTaskList(tasksByStatus.paused)}
          </TabPane>
          
          {showCompleted && (
            <TabPane 
              tab={getTabTitle('completed', tasksByStatus.completed.length)} 
              key="completed"
            >
              {renderTaskList(tasksByStatus.completed)}
            </TabPane>
          )}
          
          <TabPane 
            tab={getTabTitle('failed', tasksByStatus.failed.length)} 
            key="failed"
          >
            {renderTaskList(tasksByStatus.failed)}
          </TabPane>
          
          <TabPane 
            tab={getTabTitle('cancelled', tasksByStatus.cancelled.length)} 
            key="cancelled"
          >
            {renderTaskList(tasksByStatus.cancelled)}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default DownloadManager;
