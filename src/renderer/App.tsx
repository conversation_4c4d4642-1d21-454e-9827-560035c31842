import React, { useEffect, useState } from 'react';
import { Layout, Typography, Card, Space, Spin, Button, Avatar, Dropdown, Badge } from 'antd';
import {
  DownloadOutlined,
  BookOutlined,
  VideoCameraOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  LoginOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { LoginPanel } from './components/LoginPanel';
import { FilterPanel } from './components/FilterPanel';
import { useAuth } from './hooks/useAuth';
import { AuthState } from '../shared/services/AuthManager';
import { CourseFilters } from '../shared/types';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;

const App: React.FC = () => {
  const [appInfo, setAppInfo] = useState<{
    name: string;
    version: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentFilters, setCurrentFilters] = useState<Partial<CourseFilters>>({});
  const [showFilterPanel, setShowFilterPanel] = useState(false);

  const {
    authState,
    user,
    isLoggedIn,
    isGuestMode,
    isLoading: authLoading,
    error: authError,
    captchaRequired,
    captchaImage,
    login,
    loginAsGuest,
    logout,
    refreshCaptcha,
  } = useAuth();

  useEffect(() => {
    const loadAppInfo = async () => {
      try {
        const [name, version] = await Promise.all([
          window.electronAPI.getAppName(),
          window.electronAPI.getAppVersion(),
        ]);
        setAppInfo({ name, version });
      } catch (error) {
        console.error('Failed to load app info:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAppInfo();
  }, []);

  // 如果应用还在加载基本信息，显示加载界面
  if (loading) {
    return (
      <Layout style={{ height: '100vh' }}>
        <Content
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Spin size="large" />
        </Content>
      </Layout>
    );
  }

  // 如果用户未登录且不在访客模式，显示登录界面
  if (authState !== AuthState.LOGGED_IN && authState !== AuthState.GUEST) {
    return (
      <LoginPanel
        onLogin={login}
        onGuestMode={loginAsGuest}
        loading={authLoading}
        error={authError || undefined}
        captchaRequired={captchaRequired}
        captchaImage={captchaImage || undefined}
        onCaptchaRefresh={refreshCaptcha}
      />
    );
  }

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ];

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        // TODO: 打开个人信息页面
        console.log('打开个人信息页面');
        break;
      case 'settings':
        // TODO: 打开设置页面
        console.log('打开设置页面');
        break;
      case 'logout':
        logout();
        break;
    }
  };

  /**
   * 处理筛选条件变更
   */
  const handleFilterChange = (filters: CourseFilters) => {
    setCurrentFilters(filters);
    console.log('筛选条件已更新:', filters);
    // TODO: 根据筛选条件搜索资源
  };

  /**
   * 切换筛选面板显示状态
   */
  const toggleFilterPanel = () => {
    setShowFilterPanel(!showFilterPanel);
  };

  return (
    <Layout style={{ height: '100vh' }}>
      <Header
        style={{
          background: '#fff',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 24px',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DownloadOutlined
            style={{ fontSize: '24px', color: '#1890ff', marginRight: '12px' }}
          />
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            智慧教育下载器
          </Title>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button
            type={showFilterPanel ? 'primary' : 'default'}
            icon={<FilterOutlined />}
            onClick={toggleFilterPanel}
          >
            筛选资源
          </Button>
          
          {isLoggedIn ? (
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
            >
              <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} style={{ marginRight: '8px' }} />
                <span>{user?.displayName || user?.username}</span>
              </div>
            </Dropdown>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Badge status="default" />
              <Text type="secondary">访客模式</Text>
              <Button
                type="link"
                icon={<LoginOutlined />}
                onClick={() => window.location.reload()}
              >
                登录
              </Button>
            </div>
          )}
        </div>
      </Header>

      <Content style={{ padding: '24px', overflow: 'auto' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {showFilterPanel && (
            <div style={{ marginBottom: '24px' }}>
              <FilterPanel
                onFilterChange={handleFilterChange}
                loading={false}
                initialFilters={currentFilters}
              />
            </div>
          )}
          
          <Card>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <Title level={2}>
                  欢迎使用智慧教育下载器
                  {isLoggedIn && user && (
                    <Text type="secondary" style={{ display: 'block', fontSize: '16px', marginTop: '8px' }}>
                      {user.displayName}，欢迎回来！
                    </Text>
                  )}
                  {isGuestMode && (
                    <Text type="secondary" style={{ display: 'block', fontSize: '16px', marginTop: '8px' }}>
                      当前为访客模式，功能受限
                    </Text>
                  )}
                </Title>
                <Text type="secondary">
                  从国家中小学智慧平台下载教育资源的便捷工具
                </Text>
              </div>

              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                  gap: '16px',
                  marginTop: '32px',
                }}
              >
                <Card
                  hoverable
                  style={{ textAlign: 'center' }}
                  styles={{ body: { padding: '32px' } }}
                >
                  <BookOutlined
                    style={{
                      fontSize: '48px',
                      color: '#52c41a',
                      marginBottom: '16px',
                    }}
                  />
                  <Title level={4}>电子教材</Title>
                  <Text type="secondary">
                    下载各学科电子教材，支持PDF格式保存
                  </Text>
                  {!isLoggedIn && (
                    <div style={{ marginTop: '12px' }}>
                      <Badge status="warning" text="部分资源需要登录" />
                    </div>
                  )}
                </Card>

                <Card
                  hoverable
                  style={{ textAlign: 'center' }}
                  styles={{ body: { padding: '32px' } }}
                >
                  <VideoCameraOutlined
                    style={{
                      fontSize: '48px',
                      color: '#fa8c16',
                      marginBottom: '16px',
                    }}
                  />
                  <Title level={4}>教学视频</Title>
                  <Text type="secondary">
                    下载教学视频，自动处理M3U8格式并转换为MP4
                  </Text>
                  {!isLoggedIn && (
                    <div style={{ marginTop: '12px' }}>
                      <Badge status="warning" text="部分资源需要登录" />
                    </div>
                  )}
                </Card>
              </div>

              <div
                style={{
                  background: '#f6f8fa',
                  padding: '16px',
                  borderRadius: '8px',
                  marginTop: '24px',
                }}
              >
                <Text strong>功能特点：</Text>
                <ul style={{ marginTop: '8px', marginBottom: 0 }}>
                  <li>支持批量下载和队列管理</li>
                  <li>智能文件组织和命名</li>
                  <li>断点续传和错误重试</li>
                  <li>实时下载进度监控</li>
                  {isLoggedIn && <li>登录用户可访问更多资源</li>}
                </ul>
              </div>

              {isGuestMode && (
                <div
                  style={{
                    background: '#fff7e6',
                    border: '1px solid #ffd591',
                    padding: '16px',
                    borderRadius: '8px',
                    marginTop: '16px',
                  }}
                >
                  <Text strong style={{ color: '#fa8c16' }}>访客模式提示：</Text>
                  <div style={{ marginTop: '8px' }}>
                    <Text>
                      当前为访客模式，仅可下载公开资源。
                      <Button type="link" onClick={() => window.location.reload()}>
                        点击登录
                      </Button>
                      以获取更多教育资源。
                    </Text>
                  </div>
                </div>
              )}
            </Space>
          </Card>
        </div>
      </Content>

      <Footer
        style={{
          textAlign: 'center',
          background: '#fff',
          borderTop: '1px solid #f0f0f0',
        }}
      >
        <Text type="secondary">
          {appInfo?.name} v{appInfo?.version} | 基于 Electron + React +
          TypeScript 构建
        </Text>
      </Footer>
    </Layout>
  );
};

export default App;
