// Global styles for the Smart Edu Downloader application

* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

// Custom scrollbar styles
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Utility classes
.text-center {
  text-align: center;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Animation classes
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ResourceList component styles
.selected-resource-card {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.selected-resource-item {
  background-color: rgba(24, 144, 255, 0.05);
  border-left: 3px solid #1890ff;
  padding-left: 13px;
}

.resource-metadata-tags {
  .ant-tag {
    margin-bottom: 4px;
  }
}

.resource-stats {
  color: #666;
  font-size: 12px;
  
  .ant-divider-vertical {
    margin: 0 8px;
  }
}

// Resource access level tags
.ant-tag {
  &.access-public {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
  
  &.access-registered {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }
  
  &.access-premium {
    background-color: #fffbe6;
    border-color: #ffe58f;
    color: #faad14;
  }
}
