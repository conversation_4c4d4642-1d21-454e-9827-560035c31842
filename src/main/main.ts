import { app, BrowserWindow, ipc<PERSON>ain, dialog, shell, Notification } from 'electron';
import * as path from 'path';
import * as fs from 'fs-extra';

class SmartEduDownloaderApp {
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    this.initializeApp();
  }

  private initializeApp(): void {
    // Handle app ready
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupIpcHandlers();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // Handle app window closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
      },
      titleBarStyle: 'default',
      show: false,
    });

    // Load the renderer
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:3000');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private setupIpcHandlers(): void {
    // App info handlers
    ipcMain.handle('app:getVersion', () => {
      return app.getVersion();
    });

    ipcMain.handle('app:getName', () => {
      return app.getName();
    });

    // Download related handlers
    ipcMain.handle('download:getDefaultPath', () => {
      return app.getPath('downloads');
    });

    ipcMain.handle('download:selectPath', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        properties: ['openDirectory'],
        title: '选择下载目录'
      });

      if (result.canceled) {
        return null;
      }

      return result.filePaths[0];
    });

    ipcMain.handle('download:openFolder', async (_, folderPath: string) => {
      try {
        await shell.openPath(folderPath);
        return true;
      } catch (error) {
        console.error('Failed to open folder:', error);
        return false;
      }
    });

    // File system handlers
    ipcMain.handle('fs:checkExists', async (_, filePath: string) => {
      try {
        return await fs.pathExists(filePath);
      } catch (error) {
        return false;
      }
    });

    ipcMain.handle('fs:getStats', async (_, filePath: string) => {
      try {
        const stats = await fs.stat(filePath);
        return {
          size: stats.size,
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory(),
          mtime: stats.mtime,
          ctime: stats.ctime
        };
      } catch (error) {
        return null;
      }
    });

    // Notification handlers
    ipcMain.handle('notification:show', (_, title: string, body: string) => {
      if (Notification.isSupported()) {
        new Notification({
          title,
          body,
          icon: path.join(__dirname, '../assets/icon.png') // 如果有图标的话
        }).show();
        return true;
      }
      return false;
    });
  }
}

// Initialize the application
new SmartEduDownloaderApp();
