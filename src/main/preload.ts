import { contextBridge, ipc<PERSON>enderer } from 'electron';

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('app:getVersion'),
  getAppName: () => ipcRenderer.invoke('app:getName'),

  // Download related APIs
  getDefaultDownloadPath: () => ipcRenderer.invoke('download:getDefaultPath'),
  selectDownloadPath: () => ipcRenderer.invoke('download:selectPath'),
  openDownloadFolder: (path: string) => ipcRenderer.invoke('download:openFolder', path),

  // File system APIs
  checkFileExists: (path: string) => ipcRenderer.invoke('fs:checkExists', path),
  getFileStats: (path: string) => ipcRenderer.invoke('fs:getStats', path),

  // Notification APIs
  showNotification: (title: string, body: string) => ipcRenderer.invoke('notification:show', title, body),
});

// Type definitions are in src/shared/types/electron.d.ts
