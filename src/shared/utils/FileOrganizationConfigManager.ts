import * as fs from 'fs-extra';
import * as path from 'path';
import { FileOrganizationConfig, ValidationError, FileError } from '../types';

/**
 * 文件组织配置管理器
 * 负责加载、保存和验证文件组织配置
 */
export class FileOrganizationConfigManager {
  private static readonly DEFAULT_CONFIG_PATH = path.join(process.cwd(), 'config', 'file-organization.json');
  private static readonly DEFAULT_CONFIG: FileOrganizationConfig = {
    basePath: path.join(process.cwd(), 'downloads'),
    namingPattern: '{stage}-{grade}-{subject}-{title}',
    createSubfolders: true,
    groupBySubject: true,
    groupByGrade: true
  };

  /**
   * 加载配置文件
   * @param configPath 配置文件路径（可选）
   * @returns 文件组织配置
   */
  public static async loadConfig(configPath?: string): Promise<FileOrganizationConfig> {
    const filePath = configPath || this.DEFAULT_CONFIG_PATH;

    try {
      if (await fs.pathExists(filePath)) {
        const configData = await fs.readJson(filePath);
        const config = { ...this.DEFAULT_CONFIG, ...configData };
        this.validateConfig(config);
        return config;
      } else {
        // 配置文件不存在，返回默认配置
        return { ...this.DEFAULT_CONFIG };
      }
    } catch (error) {
      // 如果是验证错误，直接抛出
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new FileError(`加载配置文件失败: ${error.message}`, {
        originalError: error,
        context: { configPath: filePath }
      });
    }
  }

  /**
   * 保存配置文件
   * @param config 文件组织配置
   * @param configPath 配置文件路径（可选）
   */
  public static async saveConfig(config: FileOrganizationConfig, configPath?: string): Promise<void> {
    const filePath = configPath || this.DEFAULT_CONFIG_PATH;

    try {
      this.validateConfig(config);
      
      // 确保配置目录存在
      await fs.ensureDir(path.dirname(filePath));
      
      // 保存配置
      await fs.writeJson(filePath, config, { spaces: 2 });
    } catch (error) {
      // 如果是验证错误，直接抛出
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new FileError(`保存配置文件失败: ${error.message}`, {
        originalError: error,
        context: { config, configPath: filePath }
      });
    }
  }

  /**
   * 验证配置
   * @param config 文件组织配置
   */
  private static validateConfig(config: FileOrganizationConfig): void {
    if (!config.basePath || typeof config.basePath !== 'string') {
      throw new ValidationError('基础路径必须是非空字符串');
    }

    if (!config.namingPattern || typeof config.namingPattern !== 'string') {
      throw new ValidationError('命名模式必须是非空字符串');
    }

    if (typeof config.createSubfolders !== 'boolean') {
      throw new ValidationError('createSubfolders 必须是布尔值');
    }

    if (typeof config.groupBySubject !== 'boolean') {
      throw new ValidationError('groupBySubject 必须是布尔值');
    }

    if (typeof config.groupByGrade !== 'boolean') {
      throw new ValidationError('groupByGrade 必须是布尔值');
    }

    // 验证命名模式中的占位符
    const validPlaceholders = [
      '{stage}', '{grade}', '{subject}', '{version}', '{volume}',
      '{chapter}', '{lesson}', '{title}', '{type}', '{id}'
    ];

    const placeholderPattern = /\{[^}]+\}/g;
    const matches = config.namingPattern.match(placeholderPattern) || [];
    
    for (const match of matches) {
      if (!validPlaceholders.includes(match)) {
        throw new ValidationError(`无效的命名占位符: ${match}`);
      }
    }
  }

  /**
   * 获取默认配置
   * @returns 默认文件组织配置
   */
  public static getDefaultConfig(): FileOrganizationConfig {
    return { ...this.DEFAULT_CONFIG };
  }

  /**
   * 创建预设配置
   * @param presetName 预设名称
   * @returns 预设配置
   */
  public static createPresetConfig(presetName: string): FileOrganizationConfig {
    const baseConfig = this.getDefaultConfig();

    switch (presetName.toLowerCase()) {
      case 'detailed':
        return {
          ...baseConfig,
          namingPattern: '{stage}-{grade}-{subject}-{version}-{volume}-{title}',
          createSubfolders: true,
          groupBySubject: true,
          groupByGrade: true
        };

      case 'simple':
        return {
          ...baseConfig,
          namingPattern: '{subject}_{grade}_{title}',
          createSubfolders: false,
          groupBySubject: false,
          groupByGrade: false
        };

      case 'subject-based':
        return {
          ...baseConfig,
          namingPattern: '{grade}-{title}',
          createSubfolders: true,
          groupBySubject: true,
          groupByGrade: false
        };

      case 'grade-based':
        return {
          ...baseConfig,
          namingPattern: '{subject}-{title}',
          createSubfolders: true,
          groupBySubject: false,
          groupByGrade: true
        };

      case 'flat':
        return {
          ...baseConfig,
          namingPattern: '{title}',
          createSubfolders: false,
          groupBySubject: false,
          groupByGrade: false
        };

      default:
        throw new ValidationError(`未知的预设配置: ${presetName}`);
    }
  }

  /**
   * 合并配置
   * @param baseConfig 基础配置
   * @param overrideConfig 覆盖配置
   * @returns 合并后的配置
   */
  public static mergeConfig(
    baseConfig: FileOrganizationConfig,
    overrideConfig: Partial<FileOrganizationConfig>
  ): FileOrganizationConfig {
    const mergedConfig = { ...baseConfig, ...overrideConfig };
    this.validateConfig(mergedConfig);
    return mergedConfig;
  }

  /**
   * 检查配置文件是否存在
   * @param configPath 配置文件路径（可选）
   * @returns 配置文件是否存在
   */
  public static async configExists(configPath?: string): Promise<boolean> {
    const filePath = configPath || this.DEFAULT_CONFIG_PATH;
    return await fs.pathExists(filePath);
  }

  /**
   * 备份配置文件
   * @param configPath 配置文件路径（可选）
   * @returns 备份文件路径
   */
  public static async backupConfig(configPath?: string): Promise<string> {
    const filePath = configPath || this.DEFAULT_CONFIG_PATH;
    
    if (!(await fs.pathExists(filePath))) {
      throw new FileError('配置文件不存在，无法备份');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    
    try {
      await fs.copy(filePath, backupPath);
      return backupPath;
    } catch (error) {
      throw new FileError(`备份配置文件失败: ${error.message}`, {
        originalError: error,
        context: { configPath: filePath, backupPath }
      });
    }
  }

  /**
   * 恢复配置文件
   * @param backupPath 备份文件路径
   * @param configPath 目标配置文件路径（可选）
   */
  public static async restoreConfig(backupPath: string, configPath?: string): Promise<void> {
    const filePath = configPath || this.DEFAULT_CONFIG_PATH;
    
    if (!(await fs.pathExists(backupPath))) {
      throw new FileError('备份文件不存在');
    }

    try {
      // 验证备份文件的有效性
      const backupConfig = await fs.readJson(backupPath);
      this.validateConfig(backupConfig);
      
      // 恢复配置
      await fs.copy(backupPath, filePath);
    } catch (error) {
      throw new FileError(`恢复配置文件失败: ${error.message}`, {
        originalError: error,
        context: { backupPath, configPath: filePath }
      });
    }
  }

  /**
   * 重置为默认配置
   * @param configPath 配置文件路径（可选）
   */
  public static async resetToDefault(configPath?: string): Promise<void> {
    const defaultConfig = this.getDefaultConfig();
    await this.saveConfig(defaultConfig, configPath);
  }

  /**
   * 获取可用的预设配置列表
   * @returns 预设配置列表
   */
  public static getAvailablePresets(): Array<{ name: string; description: string }> {
    return [
      { name: 'detailed', description: '详细分类 - 按学段、年级、学科、版本、册次组织' },
      { name: 'simple', description: '简单模式 - 扁平结构，基本分类' },
      { name: 'subject-based', description: '按学科分类 - 主要按学科组织文件' },
      { name: 'grade-based', description: '按年级分类 - 主要按年级组织文件' },
      { name: 'flat', description: '扁平模式 - 所有文件在同一目录' }
    ];
  }

  /**
   * 验证路径是否可写
   * @param basePath 基础路径
   * @returns 路径是否可写
   */
  public static async validateBasePath(basePath: string): Promise<boolean> {
    try {
      // 确保目录存在
      await fs.ensureDir(basePath);
      
      // 测试写入权限
      const testFile = path.join(basePath, '.write-test');
      await fs.writeFile(testFile, 'test');
      await fs.remove(testFile);
      
      return true;
    } catch {
      return false;
    }
  }
}