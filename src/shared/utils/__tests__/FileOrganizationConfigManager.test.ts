import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import { FileOrganizationConfigManager } from '../FileOrganizationConfigManager';
import { FileOrganizationConfig, ValidationError, FileError } from '../../types';

// Mock fs-extra
jest.mock('fs-extra');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('FileOrganizationConfigManager', () => {
  let tempDir: string;
  let configPath: string;

  beforeEach(() => {
    tempDir = path.join(os.tmpdir(), 'config-manager-test');
    configPath = path.join(tempDir, 'file-organization.json');

    // 重置所有 mock
    jest.clearAllMocks();

    // 设置默认的 mock 行为
    mockFs.pathExists.mockResolvedValue(false);
    mockFs.readJson.mockResolvedValue({});
    mockFs.writeJson.mockResolvedValue(undefined);
    mockFs.ensureDir.mockResolvedValue(undefined);
    mockFs.copy.mockResolvedValue(undefined);
    mockFs.remove.mockResolvedValue(undefined);
    mockFs.writeFile.mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('loadConfig', () => {
    it('应该在配置文件不存在时返回默认配置', async () => {
      mockFs.pathExists.mockResolvedValue(false);

      const config = await FileOrganizationConfigManager.loadConfig(configPath);

      expect(config).toEqual(expect.objectContaining({
        basePath: expect.any(String),
        namingPattern: expect.any(String),
        createSubfolders: expect.any(Boolean),
        groupBySubject: expect.any(Boolean),
        groupByGrade: expect.any(Boolean)
      }));
    });

    it('应该在配置文件存在时加载并合并配置', async () => {
      const savedConfig = {
        basePath: '/custom/path',
        namingPattern: '{title}'
      };

      mockFs.pathExists.mockResolvedValue(true);
      mockFs.readJson.mockResolvedValue(savedConfig);

      const config = await FileOrganizationConfigManager.loadConfig(configPath);

      expect(config.basePath).toBe('/custom/path');
      expect(config.namingPattern).toBe('{title}');
      expect(config.createSubfolders).toBeDefined(); // 应该有默认值
    });

    it('应该在读取配置文件失败时抛出 FileError', async () => {
      mockFs.pathExists.mockResolvedValue(true);
      mockFs.readJson.mockRejectedValue(new Error('读取失败'));

      await expect(FileOrganizationConfigManager.loadConfig(configPath))
        .rejects.toThrow(FileError);
    });

    it('应该验证加载的配置', async () => {
      const invalidConfig = {
        basePath: '', // 无效的基础路径
        namingPattern: '{title}'
      };

      mockFs.pathExists.mockResolvedValue(true);
      mockFs.readJson.mockResolvedValue(invalidConfig);

      await expect(FileOrganizationConfigManager.loadConfig(configPath))
        .rejects.toThrow(ValidationError);
    });
  });

  describe('saveConfig', () => {
    const validConfig: FileOrganizationConfig = {
      basePath: '/test/path',
      namingPattern: '{title}',
      createSubfolders: true,
      groupBySubject: false,
      groupByGrade: true
    };

    it('应该成功保存有效配置', async () => {
      await FileOrganizationConfigManager.saveConfig(validConfig, configPath);

      expect(mockFs.ensureDir).toHaveBeenCalledWith(path.dirname(configPath));
      expect(mockFs.writeJson).toHaveBeenCalledWith(configPath, validConfig, { spaces: 2 });
    });

    it('应该在保存无效配置时抛出 ValidationError', async () => {
      const invalidConfig = { ...validConfig, basePath: '' };

      await expect(FileOrganizationConfigManager.saveConfig(invalidConfig, configPath))
        .rejects.toThrow(ValidationError);
    });

    it('应该在写入文件失败时抛出 FileError', async () => {
      mockFs.writeJson.mockRejectedValue(new Error('写入失败'));

      await expect(FileOrganizationConfigManager.saveConfig(validConfig, configPath))
        .rejects.toThrow(FileError);
    });
  });

  describe('getDefaultConfig', () => {
    it('应该返回默认配置', () => {
      const defaultConfig = FileOrganizationConfigManager.getDefaultConfig();

      expect(defaultConfig).toEqual(expect.objectContaining({
        basePath: expect.any(String),
        namingPattern: expect.any(String),
        createSubfolders: expect.any(Boolean),
        groupBySubject: expect.any(Boolean),
        groupByGrade: expect.any(Boolean)
      }));
    });

    it('应该返回配置的副本而不是原始对象', () => {
      const config1 = FileOrganizationConfigManager.getDefaultConfig();
      const config2 = FileOrganizationConfigManager.getDefaultConfig();

      expect(config1).toEqual(config2);
      expect(config1).not.toBe(config2);
    });
  });

  describe('createPresetConfig', () => {
    it('应该创建详细预设配置', () => {
      const config = FileOrganizationConfigManager.createPresetConfig('detailed');

      expect(config.namingPattern).toContain('{stage}');
      expect(config.namingPattern).toContain('{grade}');
      expect(config.namingPattern).toContain('{subject}');
      expect(config.createSubfolders).toBe(true);
      expect(config.groupBySubject).toBe(true);
      expect(config.groupByGrade).toBe(true);
    });

    it('应该创建简单预设配置', () => {
      const config = FileOrganizationConfigManager.createPresetConfig('simple');

      expect(config.createSubfolders).toBe(false);
      expect(config.groupBySubject).toBe(false);
      expect(config.groupByGrade).toBe(false);
    });

    it('应该创建按学科分类预设配置', () => {
      const config = FileOrganizationConfigManager.createPresetConfig('subject-based');

      expect(config.groupBySubject).toBe(true);
      expect(config.groupByGrade).toBe(false);
    });

    it('应该创建按年级分类预设配置', () => {
      const config = FileOrganizationConfigManager.createPresetConfig('grade-based');

      expect(config.groupBySubject).toBe(false);
      expect(config.groupByGrade).toBe(true);
    });

    it('应该创建扁平预设配置', () => {
      const config = FileOrganizationConfigManager.createPresetConfig('flat');

      expect(config.createSubfolders).toBe(false);
      expect(config.groupBySubject).toBe(false);
      expect(config.groupByGrade).toBe(false);
    });

    it('应该在未知预设名称时抛出 ValidationError', () => {
      expect(() => FileOrganizationConfigManager.createPresetConfig('unknown'))
        .toThrow(ValidationError);
    });

    it('应该支持大小写不敏感的预设名称', () => {
      const config1 = FileOrganizationConfigManager.createPresetConfig('DETAILED');
      const config2 = FileOrganizationConfigManager.createPresetConfig('detailed');

      expect(config1).toEqual(config2);
    });
  });

  describe('mergeConfig', () => {
    const baseConfig: FileOrganizationConfig = {
      basePath: '/base/path',
      namingPattern: '{title}',
      createSubfolders: true,
      groupBySubject: true,
      groupByGrade: false
    };

    it('应该成功合并配置', () => {
      const overrideConfig = {
        basePath: '/override/path',
        groupByGrade: true
      };

      const mergedConfig = FileOrganizationConfigManager.mergeConfig(baseConfig, overrideConfig);

      expect(mergedConfig.basePath).toBe('/override/path');
      expect(mergedConfig.namingPattern).toBe('{title}'); // 保持原值
      expect(mergedConfig.groupByGrade).toBe(true); // 被覆盖
      expect(mergedConfig.groupBySubject).toBe(true); // 保持原值
    });

    it('应该验证合并后的配置', () => {
      const invalidOverride = { basePath: '' };

      expect(() => FileOrganizationConfigManager.mergeConfig(baseConfig, invalidOverride))
        .toThrow(ValidationError);
    });
  });

  describe('configExists', () => {
    it('应该在配置文件存在时返回 true', async () => {
      mockFs.pathExists.mockResolvedValue(true);

      const exists = await FileOrganizationConfigManager.configExists(configPath);

      expect(exists).toBe(true);
      expect(mockFs.pathExists).toHaveBeenCalledWith(configPath);
    });

    it('应该在配置文件不存在时返回 false', async () => {
      mockFs.pathExists.mockResolvedValue(false);

      const exists = await FileOrganizationConfigManager.configExists(configPath);

      expect(exists).toBe(false);
    });
  });

  describe('backupConfig', () => {
    it('应该成功备份配置文件', async () => {
      mockFs.pathExists.mockResolvedValue(true);

      const backupPath = await FileOrganizationConfigManager.backupConfig(configPath);

      expect(backupPath).toContain('.backup.');
      expect(mockFs.copy).toHaveBeenCalledWith(configPath, backupPath);
    });

    it('应该在配置文件不存在时抛出 FileError', async () => {
      mockFs.pathExists.mockResolvedValue(false);

      await expect(FileOrganizationConfigManager.backupConfig(configPath))
        .rejects.toThrow(FileError);
    });

    it('应该在复制失败时抛出 FileError', async () => {
      mockFs.pathExists.mockResolvedValue(true);
      mockFs.copy.mockRejectedValue(new Error('复制失败'));

      await expect(FileOrganizationConfigManager.backupConfig(configPath))
        .rejects.toThrow(FileError);
    });
  });

  describe('restoreConfig', () => {
    const backupPath = '/path/to/backup.json';
    const validBackupConfig: FileOrganizationConfig = {
      basePath: '/backup/path',
      namingPattern: '{title}',
      createSubfolders: true,
      groupBySubject: true,
      groupByGrade: false
    };

    it('应该成功恢复配置文件', async () => {
      mockFs.pathExists.mockResolvedValue(true);
      mockFs.readJson.mockResolvedValue(validBackupConfig);

      await FileOrganizationConfigManager.restoreConfig(backupPath, configPath);

      expect(mockFs.copy).toHaveBeenCalledWith(backupPath, configPath);
    });

    it('应该在备份文件不存在时抛出 FileError', async () => {
      mockFs.pathExists.mockResolvedValue(false);

      await expect(FileOrganizationConfigManager.restoreConfig(backupPath, configPath))
        .rejects.toThrow(FileError);
    });

    it('应该验证备份文件的有效性', async () => {
      const invalidBackupConfig = { basePath: '' };
      mockFs.pathExists.mockResolvedValue(true);
      mockFs.readJson.mockResolvedValue(invalidBackupConfig);

      await expect(FileOrganizationConfigManager.restoreConfig(backupPath, configPath))
        .rejects.toThrow(FileError);
    });
  });

  describe('resetToDefault', () => {
    it('应该重置为默认配置', async () => {
      await FileOrganizationConfigManager.resetToDefault(configPath);

      expect(mockFs.writeJson).toHaveBeenCalledWith(
        configPath,
        expect.objectContaining({
          basePath: expect.any(String),
          namingPattern: expect.any(String)
        }),
        { spaces: 2 }
      );
    });
  });

  describe('getAvailablePresets', () => {
    it('应该返回可用预设列表', () => {
      const presets = FileOrganizationConfigManager.getAvailablePresets();

      expect(presets).toBeInstanceOf(Array);
      expect(presets.length).toBeGreaterThan(0);
      expect(presets[0]).toHaveProperty('name');
      expect(presets[0]).toHaveProperty('description');
    });

    it('应该包含所有预设配置', () => {
      const presets = FileOrganizationConfigManager.getAvailablePresets();
      const presetNames = presets.map(p => p.name);

      expect(presetNames).toContain('detailed');
      expect(presetNames).toContain('simple');
      expect(presetNames).toContain('subject-based');
      expect(presetNames).toContain('grade-based');
      expect(presetNames).toContain('flat');
    });
  });

  describe('validateBasePath', () => {
    it('应该在路径可写时返回 true', async () => {
      const testPath = '/test/path';

      const isValid = await FileOrganizationConfigManager.validateBasePath(testPath);

      expect(isValid).toBe(true);
      expect(mockFs.ensureDir).toHaveBeenCalledWith(testPath);
      expect(mockFs.writeFile).toHaveBeenCalled();
      expect(mockFs.remove).toHaveBeenCalled();
    });

    it('应该在路径不可写时返回 false', async () => {
      mockFs.ensureDir.mockRejectedValue(new Error('权限不足'));

      const isValid = await FileOrganizationConfigManager.validateBasePath('/invalid/path');

      expect(isValid).toBe(false);
    });

    it('应该在写入测试失败时返回 false', async () => {
      mockFs.writeFile.mockRejectedValue(new Error('写入失败'));

      const isValid = await FileOrganizationConfigManager.validateBasePath('/test/path');

      expect(isValid).toBe(false);
    });
  });

  describe('配置验证', () => {
    it('应该验证命名模式中的占位符', () => {
      const configWithInvalidPlaceholder: FileOrganizationConfig = {
        basePath: '/test/path',
        namingPattern: '{invalid_placeholder}',
        createSubfolders: true,
        groupBySubject: true,
        groupByGrade: false
      };

      expect(() => FileOrganizationConfigManager.mergeConfig(
        FileOrganizationConfigManager.getDefaultConfig(),
        configWithInvalidPlaceholder
      )).toThrow(ValidationError);
    });

    it('应该接受所有有效的占位符', () => {
      const validPlaceholders = [
        '{stage}', '{grade}', '{subject}', '{version}', '{volume}',
        '{chapter}', '{lesson}', '{title}', '{type}', '{id}'
      ];

      const namingPattern = validPlaceholders.join('-');
      const config: FileOrganizationConfig = {
        basePath: '/test/path',
        namingPattern,
        createSubfolders: true,
        groupBySubject: true,
        groupByGrade: false
      };

      expect(() => FileOrganizationConfigManager.mergeConfig(
        FileOrganizationConfigManager.getDefaultConfig(),
        config
      )).not.toThrow();
    });
  });
});