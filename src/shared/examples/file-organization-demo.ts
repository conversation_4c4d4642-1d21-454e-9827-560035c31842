import { FileOrganizer } from '../services/FileOrganizer';
import { CourseResource, FileOrganizationConfig } from '../types';

/**
 * 文件组织器使用示例
 * 演示如何配置和使用 FileOrganizer 来管理下载的教育资源
 */

// 示例配置 1: 详细分类组织
const detailedConfig: FileOrganizationConfig = {
  basePath: '/Users/<USER>/Documents/智慧教育资源',
  namingPattern: '{stage}-{grade}-{subject}-{version}-{volume}-{title}',
  createSubfolders: true,
  groupBySubject: true,
  groupByGrade: true
};

// 示例配置 2: 简单扁平组织
const simpleConfig: FileOrganizationConfig = {
  basePath: '/Users/<USER>/Downloads/教育资源',
  namingPattern: '{subject}_{grade}_{title}',
  createSubfolders: false,
  groupBySubject: false,
  groupByGrade: false
};

// 示例配置 3: 按学科分类
const subjectBasedConfig: FileOrganizationConfig = {
  basePath: '/Users/<USER>/Documents/课程资料',
  namingPattern: '{grade}-{title}',
  createSubfolders: true,
  groupBySubject: true,
  groupByGrade: false
};

// 示例资源数据
const sampleResources: CourseResource[] = [
  {
    id: 'math-grade3-textbook-1',
    title: '数学基础教程',
    type: 'textbook',
    url: 'https://example.com/math-textbook',
    metadata: {
      stage: '小学',
      grade: '三年级',
      subject: '数学',
      version: '人教版',
      volume: '上册',
      chapter: '第一章',
      lesson: '加法运算',
      fileSize: 15728640 // 15MB
    },
    requiresAuth: false,
    accessLevel: 'public'
  },
  {
    id: 'chinese-grade3-video-1',
    title: '古诗词朗读',
    type: 'video',
    url: 'https://example.com/chinese-video',
    metadata: {
      stage: '小学',
      grade: '三年级',
      subject: '语文',
      version: '部编版',
      volume: '上册',
      chapter: '第二章',
      lesson: '静夜思',
      fileSize: 52428800, // 50MB
      duration: 300 // 5分钟
    },
    requiresAuth: true,
    accessLevel: 'registered'
  },
  {
    id: 'english-grade4-textbook-1',
    title: 'English Basics',
    type: 'textbook',
    url: 'https://example.com/english-textbook',
    metadata: {
      stage: '小学',
      grade: '四年级',
      subject: '英语',
      version: 'PEP版',
      volume: '下册',
      chapter: '第三章',
      lesson: 'Unit 3',
      fileSize: 20971520 // 20MB
    },
    requiresAuth: false,
    accessLevel: 'public'
  }
];

/**
 * 演示详细分类组织
 */
async function demonstrateDetailedOrganization() {
  console.log('=== 详细分类组织演示 ===');
  const organizer = new FileOrganizer(detailedConfig);

  for (const resource of sampleResources) {
    try {
      // 生成文件路径
      const filePath = organizer.generatePath(resource);
      console.log(`资源: ${resource.title}`);
      console.log(`路径: ${filePath}`);
      
      // 创建目录结构
      await organizer.createDirectoryStructure(filePath);
      console.log(`✓ 目录结构已创建`);
      
      // 检查重复文件
      const isDuplicate = await organizer.checkDuplicateFile(filePath, resource);
      console.log(`重复检查: ${isDuplicate ? '是重复文件' : '非重复文件'}`);
      
      // 检查版本更新
      const needsUpdate = await organizer.checkVersionUpdate(filePath, resource);
      console.log(`版本检查: ${needsUpdate ? '需要更新' : '无需更新'}`);
      
      console.log('---');
    } catch (error) {
      console.error(`处理资源 ${resource.title} 时出错:`, error.message);
    }
  }
}

/**
 * 演示简单扁平组织
 */
async function demonstrateSimpleOrganization() {
  console.log('=== 简单扁平组织演示 ===');
  const organizer = new FileOrganizer(simpleConfig);

  for (const resource of sampleResources) {
    const filePath = organizer.generatePath(resource);
    console.log(`${resource.title} -> ${filePath}`);
  }
  console.log('---');
}

/**
 * 演示按学科分类组织
 */
async function demonstrateSubjectBasedOrganization() {
  console.log('=== 按学科分类组织演示 ===');
  const organizer = new FileOrganizer(subjectBasedConfig);

  for (const resource of sampleResources) {
    const filePath = organizer.generatePath(resource);
    console.log(`${resource.title} -> ${filePath}`);
  }
  console.log('---');
}

/**
 * 演示文件注册和统计功能
 */
async function demonstrateFileRegistry() {
  console.log('=== 文件注册和统计演示 ===');
  const organizer = new FileOrganizer(detailedConfig);

  // 模拟注册文件
  for (const resource of sampleResources) {
    const filePath = organizer.generatePath(resource);
    try {
      // 在实际使用中，这里会是真实的文件路径
      await organizer.registerFile(resource, filePath);
      console.log(`✓ 已注册: ${resource.title}`);
    } catch (error) {
      console.log(`✗ 注册失败: ${resource.title} - ${error.message}`);
    }
  }

  // 获取统计信息
  const stats = organizer.getRegistryStats();
  console.log('注册表统计:');
  console.log(`- 总文件数: ${stats.totalFiles}`);
  console.log(`- 有效文件: ${stats.validFiles}`);
  console.log(`- 无效文件: ${stats.invalidFiles}`);
  console.log(`- 总大小: ${Math.round(stats.totalSize / 1024 / 1024)}MB`);
  console.log('---');
}

/**
 * 演示配置管理
 */
function demonstrateConfigManagement() {
  console.log('=== 配置管理演示 ===');
  const organizer = new FileOrganizer(detailedConfig);

  console.log('初始配置:');
  console.log(JSON.stringify(organizer.getConfig(), null, 2));

  // 更新配置
  organizer.updateConfig({
    basePath: '/新的/基础/路径',
    namingPattern: '{subject}_{title}'
  });

  console.log('更新后配置:');
  console.log(JSON.stringify(organizer.getConfig(), null, 2));
  console.log('---');
}

/**
 * 演示自定义命名模式
 */
function demonstrateCustomNamingPatterns() {
  console.log('=== 自定义命名模式演示 ===');
  
  const patterns = [
    '{stage}_{grade}_{subject}_{title}',
    '{subject}-{grade}-{title}',
    '{grade}年级-{subject}-{title}',
    '[{version}]{subject}_{grade}_{title}',
    '{subject}/{grade}/{title}'
  ];

  patterns.forEach((pattern, index) => {
    console.log(`模式 ${index + 1}: ${pattern}`);
    const config = { ...detailedConfig, namingPattern: pattern };
    const organizer = new FileOrganizer(config);
    
    const sampleResource = sampleResources[0];
    const filePath = organizer.generatePath(sampleResource);
    const fileName = filePath.split('/').pop();
    console.log(`结果: ${fileName}`);
    console.log('');
  });
}

/**
 * 主演示函数
 */
export async function runFileOrganizationDemo() {
  console.log('🗂️  文件组织器功能演示\n');

  try {
    await demonstrateDetailedOrganization();
    await demonstrateSimpleOrganization();
    await demonstrateSubjectBasedOrganization();
    await demonstrateFileRegistry();
    demonstrateConfigManagement();
    demonstrateCustomNamingPatterns();
    
    console.log('✅ 演示完成！');
  } catch (error) {
    console.error('❌ 演示过程中出现错误:', error);
  }
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  runFileOrganizationDemo();
}