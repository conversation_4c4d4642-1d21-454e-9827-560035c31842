// Type definitions for Electron API exposed to renderer process

export interface ElectronAPI {
  // App info
  getAppVersion: () => Promise<string>;
  getAppName: () => Promise<string>;

  // Download related APIs
  getDefaultDownloadPath: () => Promise<string>;
  selectDownloadPath: () => Promise<string | null>;
  openDownloadFolder: (path: string) => Promise<boolean>;

  // File system APIs
  checkFileExists: (path: string) => Promise<boolean>;
  getFileStats: (path: string) => Promise<{
    size: number;
    isFile: boolean;
    isDirectory: boolean;
    mtime: Date;
    ctime: Date;
  } | null>;

  // Notification APIs
  showNotification: (title: string, body: string) => Promise<boolean>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
