// Shared type definitions for the Smart Edu Downloader application

export interface AppInfo {
  name: string;
  version: string;
}

// ============================================================================
// 筛选和资源相关类型 (Filter and Resource Types)
// ============================================================================

export interface CourseFilters {
  stage: string;      // 学段
  grade: string;      // 年级
  subject: string;    // 学科
  version: string;    // 版本
  volume: string;     // 册次
}

export interface FilterOption {
  value: string;
  label: string;
  children?: FilterOption[];
}

export interface FilterOptions {
  stages: FilterOption[];
  grades: FilterOption[];
  subjects: FilterOption[];
  versions: FilterOption[];
  volumes: FilterOption[];
}

export interface CourseResource {
  id: string;
  title: string;
  type: 'textbook' | 'video';
  url: string;
  metadata: ResourceMetadata;
  requiresAuth: boolean;
  accessLevel: 'public' | 'registered' | 'premium';
}

export interface ResourceMetadata {
  stage: string;
  grade: string;
  subject: string;
  version: string;
  volume: string;
  chapter?: string;
  lesson?: string;
  fileSize?: number;
  duration?: number;
}

export interface ResourceDetail extends CourseResource {
  description?: string;
  thumbnailUrl?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TextbookInfo {
  id: string;
  title: string;
  pages: number;
  format: 'pdf' | 'epub' | 'other';
  downloadUrl: string;
  previewUrl?: string;
}

export interface VideoInfo {
  id: string;
  title: string;
  duration: number;
  quality: string;
  format: 'mp4' | 'm3u8' | 'other';
  playlistUrl: string;
  thumbnailUrl?: string;
}

// ============================================================================
// 用户认证相关类型 (Authentication Types)
// ============================================================================

export interface LoginCredentials {
  username: string;
  password: string;
  captcha?: string;
  captchaToken?: string;
}

export interface AuthResult {
  success: boolean;
  token?: string;
  user?: UserInfo;
  error?: string;
  requiresCaptcha?: boolean;
  captchaImage?: string;
}

export interface UserInfo {
  id: string;
  username: string;
  displayName: string;
  permissions: string[];
}

export interface CaptchaInfo {
  image: string; // base64编码的验证码图片
  token: string; // 验证码token
}

// ============================================================================
// 下载管理相关类型 (Download Management Types)
// ============================================================================

export type TaskStatus = 'pending' | 'downloading' | 'paused' | 'completed' | 'failed' | 'cancelled' | 'resuming';

export interface DownloadTask {
  id: string;
  resource: CourseResource;
  status: TaskStatus;
  progress: number;
  speed: number;
  estimatedTime: number;
  error?: string;
  requiresAuth: boolean;
  createdAt: Date;
  updatedAt: Date;
  outputPath: string;
  retryCount: number;
  maxRetries: number;
  // 断点续传相关字段
  canResume?: boolean;
  resumeState?: ResumeTaskState;
  lastResumeAt?: Date;
}

/**
 * 断点续传任务状态
 */
export interface ResumeTaskState {
  totalSegments: number;
  downloadedSegments: number;
  failedSegments: number;
  tempDir: string;
  lastSavedAt: Date;
  resumeCount: number;
}

export interface DownloadProgress {
  taskId: string;
  progress: number;
  speed: number;
  downloadedBytes: number;
  totalBytes: number;
  estimatedTime: number;
  status: TaskStatus;
  // 断点续传相关进度信息
  resumedFromProgress?: number;
  segmentsCompleted?: number;
  segmentsTotal?: number;
  isResuming?: boolean;
}

/**
 * 网络状态信息
 */
export interface NetworkStatus {
  isOnline: boolean;
  connectionType: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  effectiveType: 'slow-2g' | '2g' | '3g' | '4g' | 'unknown';
  downlink: number; // Mbps
  rtt: number; // ms
  saveData: boolean;
  lastChecked: Date;
}

/**
 * 断点续传事件类型
 */
export type ResumeEventType =
  | 'resume-started'
  | 'resume-progress'
  | 'resume-completed'
  | 'resume-failed'
  | 'network-reconnected'
  | 'segment-resumed';

export interface BatchDownloadOptions {
  maxConcurrent: number;
  retryOnFailure: boolean;
  skipExisting: boolean;
  outputDirectory: string;
}

// 下载配置相关类型
export interface DownloadConfig {
  maxConcurrentDownloads: number;
  chunkSize: number;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  enableResume: boolean;
}

// 批量下载结果
export interface BatchDownloadResult {
  successful: DownloadTask[];
  failed: Array<{
    resource: CourseResource;
    error: string;
  }>;
  totalCount: number;
  successCount: number;
  failureCount: number;
}

// 下载统计信息
export interface DownloadStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  activeTasks: number;
  totalDownloadedBytes: number;
  averageSpeed: number;
}

// ============================================================================
// M3U8 视频处理相关类型 (M3U8 Video Processing Types)
// ============================================================================

export interface M3U8Playlist {
  baseUrl: string;
  segments: M3U8Segment[];
  totalDuration: number;
  isEncrypted: boolean;
  keyUrl?: string;
}

export interface M3U8Segment {
  url: string;
  duration: number;
  sequence: number;
  byteRange?: {
    start: number;
    length: number;
  };
}

export interface VideoMergeOptions {
  outputPath: string;
  quality?: string;
  format: 'mp4' | 'avi' | 'mkv';
  deleteSegments: boolean;
}

// ============================================================================
// 文件组织相关类型 (File Organization Types)
// ============================================================================

export interface FileOrganizationConfig {
  basePath: string;
  namingPattern: string;
  createSubfolders: boolean;
  groupBySubject: boolean;
  groupByGrade: boolean;
}

export interface FileInfo {
  path: string;
  size: number;
  checksum: string;
  createdAt: Date;
  isValid: boolean;
}

// ============================================================================
// 系统配置相关类型 (System Configuration Types)
// ============================================================================

export interface AppConfig {
  downloadPath: string;
  maxConcurrentDownloads: number;
  requestTimeout: number;
  retryAttempts: number;
  userAgent: string;
  proxySettings?: ProxySettings;
  fileOrganization: FileOrganizationConfig;
}

export interface ProxySettings {
  enabled: boolean;
  host: string;
  port: number;
  username?: string;
  password?: string;
}

export interface SystemStatus {
  diskSpace: {
    available: number;
    total: number;
  };
  memoryUsage: {
    used: number;
    total: number;
  };
  networkStatus: 'online' | 'offline' | 'limited';
  activeDownloads: number;
}

// ============================================================================
// 错误处理相关类型 (Error Handling Types)
// ============================================================================

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  PARSE_ERROR = 'PARSE_ERROR',
  FILE_ERROR = 'FILE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  API_ERROR = 'API_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  DOWNLOAD_ERROR = 'DOWNLOAD_ERROR',
  M3U8_ERROR = 'M3U8_ERROR',
  FFMPEG_ERROR = 'FFMPEG_ERROR',
  DISK_SPACE_ERROR = 'DISK_SPACE_ERROR'
}

export interface ErrorDetails {
  code?: string;
  statusCode?: number;
  originalError?: Error;
  context?: Record<string, any>;
  timestamp: Date;
}

export class SmartEduError extends Error {
  public readonly type: ErrorType;
  public readonly details: ErrorDetails;
  public readonly isRetryable: boolean;

  constructor(
    type: ErrorType,
    message: string,
    details: Partial<ErrorDetails> = {},
    isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'SmartEduError';
    this.type = type;
    this.isRetryable = isRetryable;
    this.details = {
      timestamp: new Date(),
      ...details
    };
  }
}

export class NetworkError extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.NETWORK_ERROR, message, details, true);
    this.name = 'NetworkError';
  }
}

export class ParseError extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.PARSE_ERROR, message, details, false);
    this.name = 'ParseError';
  }
}

export class FileError extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.FILE_ERROR, message, details, false);
    this.name = 'FileError';
  }
}

export class ValidationError extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.VALIDATION_ERROR, message, details, false);
    this.name = 'ValidationError';
  }
}

export class ApiError extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.API_ERROR, message, details, true);
    this.name = 'ApiError';
  }
}

export class AuthError extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.AUTH_ERROR, message, details, false);
    this.name = 'AuthError';
  }
}

export class DownloadError extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.DOWNLOAD_ERROR, message, details, true);
    this.name = 'DownloadError';
  }
}

export class M3U8Error extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.M3U8_ERROR, message, details, true);
    this.name = 'M3U8Error';
  }
}

export class FFmpegError extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.FFMPEG_ERROR, message, details, false);
    this.name = 'FFmpegError';
  }
}

export class DiskSpaceError extends SmartEduError {
  constructor(message: string, details: Partial<ErrorDetails> = {}) {
    super(ErrorType.DISK_SPACE_ERROR, message, details, false);
    this.name = 'DiskSpaceError';
  }
}

// ============================================================================
// UI 组件属性接口 (UI Component Props Interfaces)
// ============================================================================

export interface FilterPanelProps {
  onFilterChange: (filters: CourseFilters) => void;
  loading: boolean;
  initialFilters?: Partial<CourseFilters>;
  disabled?: boolean;
}

export interface LoginPanelProps {
  onLogin: (credentials: LoginCredentials) => void;
  onGuestMode: () => void;
  loading: boolean;
  error?: string;
  captchaRequired?: boolean;
  captchaImage?: string;
  onCaptchaRefresh?: () => void;
}

export interface ResourceListProps {
  resources: CourseResource[];
  onDownload: (resource: CourseResource) => void;
  onBatchDownload: (resources: CourseResource[]) => void;
  userLoggedIn: boolean;
  loading?: boolean;
  selectedResources?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
}

export interface DownloadManagerProps {
  tasks: DownloadTask[];
  onPause: (taskId: string) => void;
  onResume: (taskId: string) => void;
  onCancel: (taskId: string) => void;
  onRetry: (taskId: string) => void;
  onClear: (taskId: string) => void;
  showCompleted?: boolean;
}

export interface ProgressBarProps {
  progress: number;
  speed?: number;
  estimatedTime?: number;
  status: TaskStatus;
  showDetails?: boolean;
}

export interface SystemStatusProps {
  status: SystemStatus;
  onRefresh?: () => void;
  showDetails?: boolean;
}

// ============================================================================
// 重试机制相关类型 (Retry Mechanism Types)
// ============================================================================

export interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryableErrors: ErrorType[];
}

export interface RetryState {
  attempt: number;
  lastError?: Error;
  nextRetryAt?: Date;
  totalDelay: number;
}

// ============================================================================
// 事件和回调类型 (Event and Callback Types)
// ============================================================================

export type ProgressCallback = (progress: DownloadProgress) => void;
export type ErrorCallback = (error: SmartEduError) => void;
export type CompletionCallback = (result: { success: boolean; data?: any; error?: SmartEduError }) => void;

export interface EventHandlers {
  onProgress?: ProgressCallback;
  onError?: ErrorCallback;
  onComplete?: CompletionCallback;
  onStart?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  onCancel?: () => void;
}

// ============================================================================
// API 响应类型 (API Response Types)
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ============================================================================
// 缓存相关类型 (Cache Types)
// ============================================================================

export interface CacheEntry<T> {
  data: T;
  timestamp: Date;
  expiresAt: Date;
  key: string;
}

export interface CacheOptions {
  ttl: number; // Time to live in milliseconds
  maxSize: number;
  cleanupInterval: number;
}

// ============================================================================
// 日志相关类型 (Logging Types)
// ============================================================================

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  context?: Record<string, any>;
  error?: Error;
}