import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import {
  CourseFilters,
  FilterOptions,
  CourseResource,
  ResourceDetail,
  M3U8Playlist,
  CaptchaInfo,
  AuthResult,
  ApiResponse,
  NetworkError,
  ApiError,
  ParseError,
  RetryOptions,
  RetryState
} from '../types';

/**
 * 智慧平台API客户端
 * 提供与国家中小学智慧平台的API交互功能
 */
export class SmartEduClient {
  private axiosInstance: AxiosInstance;
  private userAgents: string[];
  private currentUserAgentIndex: number = 0;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;
  private readonly minRequestInterval: number = 1000; // 最小请求间隔 1秒
  private readonly maxRequestsPerMinute: number = 30; // 每分钟最大请求数
  private requestTimes: number[] = [];

  private readonly defaultRetryOptions: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryableErrors: ['NETWORK_ERROR', 'API_ERROR'] as any[]
  };

  constructor(baseURL: string = 'https://basic.smartedu.cn') {
    // 初始化User-Agent列表
    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ];

    // 创建axios实例
    this.axiosInstance = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    // 设置请求拦截器
    this.setupRequestInterceptor();
    // 设置响应拦截器
    this.setupResponseInterceptor();
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor(): void {
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 轮换User-Agent
        config.headers['User-Agent'] = this.getNextUserAgent();
        
        // 添加常见的浏览器请求头
        config.headers['Referer'] = 'https://basic.smartedu.cn/';
        config.headers['Origin'] = 'https://basic.smartedu.cn';
        
        return config;
      },
      (error) => {
        return Promise.reject(new NetworkError('请求配置失败', { originalError: error }));
      }
    );
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor(): void {
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (error.response) {
          // 服务器响应了错误状态码
          const statusCode = error.response.status;
          const message = `API请求失败: ${statusCode} ${error.response.statusText}`;
          throw new ApiError(message, {
            statusCode,
            originalError: error,
            context: { url: error.config?.url, method: error.config?.method }
          });
        } else if (error.request) {
          // 请求已发出但没有收到响应
          throw new NetworkError('网络请求超时或无响应', { originalError: error });
        } else {
          // 其他错误
          throw new NetworkError('请求配置错误', { originalError: error });
        }
      }
    );
  }

  /**
   * 获取下一个User-Agent
   */
  private getNextUserAgent(): string {
    const userAgent = this.userAgents[this.currentUserAgentIndex];
    this.currentUserAgentIndex = (this.currentUserAgentIndex + 1) % this.userAgents.length;
    return userAgent;
  }

  /**
   * 请求频率限制
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    
    // 清理超过1分钟的请求记录
    this.requestTimes = this.requestTimes.filter(time => now - time < 60000);
    
    // 检查每分钟请求数限制
    if (this.requestTimes.length >= this.maxRequestsPerMinute) {
      const oldestRequest = Math.min(...this.requestTimes);
      const waitTime = 60000 - (now - oldestRequest);
      if (waitTime > 0) {
        await this.delay(waitTime);
      }
    }
    
    // 检查最小请求间隔
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.minRequestInterval) {
      await this.delay(this.minRequestInterval - timeSinceLastRequest);
    }
    
    // 记录请求时间
    this.requestTimes.push(Date.now());
    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 带重试机制的请求执行
   */
  private async executeWithRetry<T>(
    operation: () => Promise<AxiosResponse<T>>,
    options: Partial<RetryOptions> = {}
  ): Promise<AxiosResponse<T>> {
    const retryOptions = { ...this.defaultRetryOptions, ...options };
    const retryState: RetryState = {
      attempt: 0,
      totalDelay: 0
    };

    while (retryState.attempt <= retryOptions.maxRetries) {
      try {
        // 执行请求频率限制
        await this.enforceRateLimit();
        
        // 执行操作
        const result = await operation();
        return result;
      } catch (error) {
        retryState.attempt++;
        retryState.lastError = error as Error;

        // 检查是否应该重试
        if (
          retryState.attempt > retryOptions.maxRetries ||
          !this.shouldRetry(error as Error, retryOptions)
        ) {
          throw error;
        }

        // 计算延迟时间
        const delay = Math.min(
          retryOptions.baseDelay * Math.pow(retryOptions.backoffFactor, retryState.attempt - 1),
          retryOptions.maxDelay
        );

        retryState.totalDelay += delay;
        retryState.nextRetryAt = new Date(Date.now() + delay);

        console.warn(`请求失败，${delay}ms后进行第${retryState.attempt}次重试:`, error);
        await this.delay(delay);
      }
    }

    throw retryState.lastError;
  }

  /**
   * 判断错误是否应该重试
   */
  private shouldRetry(error: Error, options: RetryOptions): boolean {
    if (error instanceof NetworkError || error instanceof ApiError) {
      return options.retryableErrors.includes(error.type as any);
    }
    return false;
  }

  /**
   * 通用GET请求
   */
  private async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.executeWithRetry(() => 
        this.axiosInstance.get<T>(url, config)
      );
      
      return {
        success: true,
        data: response.data,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
        timestamp: new Date()
      };
    }
  }

  /**
   * 通用POST请求
   */
  private async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.executeWithRetry(() => 
        this.axiosInstance.post<T>(url, data, config)
      );
      
      return {
        success: true,
        data: response.data,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
        timestamp: new Date()
      };
    }
  }

  /**
   * 登录
   */
  async login(username: string, password: string, captcha?: string): Promise<AuthResult> {
    try {
      const loginData = {
        username,
        password,
        captcha
      };

      const response = await this.post<AuthResult>('/api/auth/login', loginData);
      
      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || '登录失败'
        };
      }

      return response.data;
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * 登出
   */
  async logout(): Promise<void> {
    await this.post('/api/auth/logout');
  }

  /**
   * 检查认证状态
   */
  async checkAuthStatus(): Promise<boolean> {
    try {
      const response = await this.get<{ authenticated: boolean }>('/api/auth/status');
      return response.success && response.data?.authenticated === true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取筛选选项 - 支持级联获取
   */
  async getFilterOptions(parentFilter?: Partial<CourseFilters>): Promise<FilterOptions> {
    try {
      const params = parentFilter ? { ...parentFilter } : {};
      const response = await this.get<FilterOptions>('/syncClassroom/getFilterOptions', { params });
      
      if (!response.success || !response.data) {
        throw new ApiError('获取筛选选项失败', { context: { parentFilter } });
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取筛选选项失败', { originalError: error as Error });
    }
  }

  /**
   * 获取学段列表
   */
  async getStages(): Promise<FilterOption[]> {
    try {
      const response = await this.get<FilterOption[]>('/syncClassroom/stages');
      
      if (!response.success || !response.data) {
        throw new ApiError('获取学段列表失败');
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取学段列表失败', { originalError: error as Error });
    }
  }

  /**
   * 根据学段获取年级列表
   */
  async getGradesByStage(stage: string): Promise<FilterOption[]> {
    try {
      const response = await this.get<FilterOption[]>('/syncClassroom/grades', {
        params: { stage }
      });
      
      if (!response.success || !response.data) {
        throw new ApiError('获取年级列表失败', { context: { stage } });
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取年级列表失败', { originalError: error as Error });
    }
  }

  /**
   * 根据学段和年级获取学科列表
   */
  async getSubjectsByStageAndGrade(stage: string, grade: string): Promise<FilterOption[]> {
    try {
      const response = await this.get<FilterOption[]>('/syncClassroom/subjects', {
        params: { stage, grade }
      });
      
      if (!response.success || !response.data) {
        throw new ApiError('获取学科列表失败', { context: { stage, grade } });
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取学科列表失败', { originalError: error as Error });
    }
  }

  /**
   * 根据学段、年级和学科获取版本列表
   */
  async getVersionsByStageGradeSubject(stage: string, grade: string, subject: string): Promise<FilterOption[]> {
    try {
      const response = await this.get<FilterOption[]>('/syncClassroom/versions', {
        params: { stage, grade, subject }
      });
      
      if (!response.success || !response.data) {
        throw new ApiError('获取版本列表失败', { context: { stage, grade, subject } });
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取版本列表失败', { originalError: error as Error });
    }
  }

  /**
   * 根据学段、年级、学科和版本获取册次列表
   */
  async getVolumesByStageGradeSubjectVersion(
    stage: string, 
    grade: string, 
    subject: string, 
    version: string
  ): Promise<FilterOption[]> {
    try {
      const response = await this.get<FilterOption[]>('/syncClassroom/volumes', {
        params: { stage, grade, subject, version }
      });
      
      if (!response.success || !response.data) {
        throw new ApiError('获取册次列表失败', { 
          context: { stage, grade, subject, version } 
        });
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取册次列表失败', { originalError: error as Error });
    }
  }

  /**
   * 搜索资源
   */
  async searchResources(filters: CourseFilters): Promise<CourseResource[]> {
    try {
      const response = await this.get<CourseResource[]>('/syncClassroom/search', { 
        params: filters 
      });
      
      if (!response.success || !response.data) {
        throw new ApiError('搜索资源失败', { context: { filters } });
      }

      // 对资源进行访问权限检查和处理
      const processedResources = await this.processResourcesWithPermissions(response.data);
      return processedResources;
    } catch (error) {
      throw new ApiError('搜索资源失败', { originalError: error as Error });
    }
  }

  /**
   * 处理资源权限信息
   */
  private async processResourcesWithPermissions(resources: CourseResource[]): Promise<CourseResource[]> {
    const isAuthenticated = await this.checkAuthStatus();
    
    return resources.map(resource => ({
      ...resource,
      // 根据认证状态和资源类型确定访问权限
      requiresAuth: this.determineAuthRequirement(resource),
      accessLevel: this.determineAccessLevel(resource, isAuthenticated)
    }));
  }

  /**
   * 确定资源是否需要认证
   */
  private determineAuthRequirement(resource: CourseResource): boolean {
    // 视频资源通常需要认证
    if (resource.type === 'video') {
      return true;
    }
    
    // 某些高级教材可能需要认证
    if (resource.metadata.stage === 'high' || resource.metadata.subject === 'advanced') {
      return true;
    }
    
    return resource.requiresAuth || false;
  }

  /**
   * 确定资源访问级别
   */
  private determineAccessLevel(resource: CourseResource, isAuthenticated: boolean): 'public' | 'registered' | 'premium' {
    if (!this.determineAuthRequirement(resource)) {
      return 'public';
    }
    
    if (isAuthenticated) {
      // 已认证用户可以访问注册级别的资源
      return resource.accessLevel === 'premium' ? 'premium' : 'registered';
    }
    
    return resource.accessLevel || 'registered';
  }

  /**
   * 获取资源详情
   */
  async getResourceDetail(resourceId: string): Promise<ResourceDetail> {
    try {
      const response = await this.get<ResourceDetail>(`/api/resource/${resourceId}`);
      
      if (!response.success || !response.data) {
        throw new ApiError('获取资源详情失败', { context: { resourceId } });
      }

      // 检查用户权限并处理资源详情
      const processedDetail = await this.processResourceDetailWithPermissions(response.data);
      return processedDetail;
    } catch (error) {
      throw new ApiError('获取资源详情失败', { originalError: error as Error });
    }
  }

  /**
   * 处理资源详情的权限信息
   */
  private async processResourceDetailWithPermissions(detail: ResourceDetail): Promise<ResourceDetail> {
    const isAuthenticated = await this.checkAuthStatus();
    
    return {
      ...detail,
      requiresAuth: this.determineAuthRequirement(detail),
      accessLevel: this.determineAccessLevel(detail, isAuthenticated)
    };
  }

  /**
   * 检查用户对特定资源的访问权限
   */
  async checkResourceAccess(resourceId: string): Promise<{
    hasAccess: boolean;
    requiresAuth: boolean;
    accessLevel: 'public' | 'registered' | 'premium';
    message?: string;
  }> {
    try {
      const isAuthenticated = await this.checkAuthStatus();
      const response = await this.get<{
        hasAccess: boolean;
        requiresAuth: boolean;
        accessLevel: string;
        message?: string;
      }>(`/api/resource/${resourceId}/access`);
      
      if (!response.success || !response.data) {
        // 如果API不可用，使用本地逻辑判断
        return {
          hasAccess: !isAuthenticated ? false : true,
          requiresAuth: true,
          accessLevel: isAuthenticated ? 'registered' : 'public',
          message: isAuthenticated ? undefined : '需要登录才能访问此资源'
        };
      }

      return {
        ...response.data,
        accessLevel: response.data.accessLevel as 'public' | 'registered' | 'premium'
      };
    } catch (error) {
      // 发生错误时的默认处理
      const isAuthenticated = await this.checkAuthStatus();
      return {
        hasAccess: isAuthenticated,
        requiresAuth: true,
        accessLevel: isAuthenticated ? 'registered' : 'public',
        message: '无法验证资源访问权限'
      };
    }
  }

  /**
   * 获取视频播放列表
   */
  async getVideoPlaylist(videoId: string): Promise<M3U8Playlist> {
    try {
      const response = await this.get<M3U8Playlist>(`/api/video/${videoId}/playlist`);
      
      if (!response.success || !response.data) {
        throw new ApiError('获取视频播放列表失败', { context: { videoId } });
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取视频播放列表失败', { originalError: error as Error });
    }
  }

  /**
   * 获取验证码
   */
  async getCaptcha(): Promise<CaptchaInfo> {
    try {
      const response = await this.get<CaptchaInfo>('/api/auth/captcha');
      
      if (!response.success || !response.data) {
        throw new ApiError('获取验证码失败');
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取验证码失败', { originalError: error as Error });
    }
  }

  /**
   * 获取请求统计信息
   */
  getRequestStats(): {
    totalRequests: number;
    requestsInLastMinute: number;
    currentUserAgent: string;
    lastRequestTime: Date | null;
  } {
    const now = Date.now();
    const requestsInLastMinute = this.requestTimes.filter(time => now - time < 60000).length;
    
    return {
      totalRequests: this.requestCount,
      requestsInLastMinute,
      currentUserAgent: this.userAgents[this.currentUserAgentIndex],
      lastRequestTime: this.lastRequestTime > 0 ? new Date(this.lastRequestTime) : null
    };
  }

  /**
   * 重置请求统计
   */
  resetRequestStats(): void {
    this.requestCount = 0;
    this.requestTimes = [];
    this.lastRequestTime = 0;
  }

  /**
   * 设置自定义User-Agent列表
   */
  setUserAgents(userAgents: string[]): void {
    if (userAgents.length === 0) {
      throw new Error('User-Agent列表不能为空');
    }
    this.userAgents = [...userAgents];
    this.currentUserAgentIndex = 0;
  }

  /**
   * 获取当前配置
   */
  getConfig(): {
    baseURL: string;
    timeout: number;
    minRequestInterval: number;
    maxRequestsPerMinute: number;
    userAgentCount: number;
  } {
    return {
      baseURL: this.axiosInstance.defaults.baseURL || '',
      timeout: this.axiosInstance.defaults.timeout || 0,
      minRequestInterval: this.minRequestInterval,
      maxRequestsPerMinute: this.maxRequestsPerMinute,
      userAgentCount: this.userAgents.length
    };
  }
}

export default SmartEduClient;