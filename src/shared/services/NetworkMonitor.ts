import { EventEmitter } from 'events';
import axios from 'axios';
import { NetworkStatus } from '../types';

/**
 * 网络监控配置
 */
export interface NetworkMonitorConfig {
  checkInterval: number; // 检查间隔（毫秒）
  timeoutDuration: number; // 超时时间（毫秒）
  testUrls: string[]; // 用于测试网络连接的URL列表
  retryAttempts: number; // 重试次数
  enableDetailedInfo: boolean; // 是否启用详细网络信息
}

/**
 * 网络连接事件
 */
export interface NetworkEvent {
  type: 'online' | 'offline' | 'slow' | 'fast' | 'reconnected';
  status: NetworkStatus;
  timestamp: Date;
  previousStatus?: NetworkStatus;
}

/**
 * 网络状态监控器
 * 监控网络连接状态，检测网络中断和恢复
 */
export class NetworkMonitor extends EventEmitter {
  private config: NetworkMonitorConfig;
  private currentStatus: NetworkStatus;
  private checkTimer?: NodeJS.Timeout;
  private isMonitoring: boolean = false;
  private lastOnlineTime: Date = new Date();
  private consecutiveFailures: number = 0;

  private readonly defaultConfig: NetworkMonitorConfig = {
    checkInterval: 5000, // 5秒
    timeoutDuration: 10000, // 10秒
    testUrls: [
      'https://www.google.com/generate_204',
      'https://www.baidu.com',
      'https://httpbin.org/status/200'
    ],
    retryAttempts: 3,
    enableDetailedInfo: false
  };

  constructor(config?: Partial<NetworkMonitorConfig>) {
    super();
    this.config = { ...this.defaultConfig, ...config };
    
    // 初始化网络状态
    this.currentStatus = {
      isOnline: navigator.onLine,
      connectionType: 'unknown',
      effectiveType: 'unknown',
      downlink: 0,
      rtt: 0,
      saveData: false,
      lastChecked: new Date()
    };

    this.setupBrowserEventListeners();
  }

  /**
   * 开始监控网络状态
   */
  startMonitoring(): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.checkNetworkStatus();
    
    this.checkTimer = setInterval(() => {
      this.checkNetworkStatus();
    }, this.config.checkInterval);

    this.emit('monitoring-started');
  }

  /**
   * 停止监控网络状态
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = undefined;
    }

    this.emit('monitoring-stopped');
  }

  /**
   * 获取当前网络状态
   */
  getCurrentStatus(): NetworkStatus {
    return { ...this.currentStatus };
  }

  /**
   * 检查网络连接状态
   */
  private async checkNetworkStatus(): Promise<void> {
    const startTime = Date.now();
    let isOnline = false;
    let rtt = 0;

    try {
      // 尝试连接测试URL
      const testPromises = this.config.testUrls.map(url => 
        this.testConnection(url)
      );

      const results = await Promise.allSettled(testPromises);
      const successfulTests = results.filter(result => result.status === 'fulfilled');
      
      if (successfulTests.length > 0) {
        isOnline = true;
        // 计算平均RTT
        const rtts = successfulTests.map(result => 
          (result as PromiseFulfilledResult<number>).value
        );
        rtt = rtts.reduce((sum, val) => sum + val, 0) / rtts.length;
        this.consecutiveFailures = 0;
        this.lastOnlineTime = new Date();
      } else {
        isOnline = false;
        this.consecutiveFailures++;
      }
    } catch (error) {
      isOnline = false;
      this.consecutiveFailures++;
    }

    const previousStatus = { ...this.currentStatus };
    
    // 更新网络状态
    this.currentStatus = {
      isOnline,
      connectionType: this.getConnectionType(),
      effectiveType: this.getEffectiveType(rtt),
      downlink: this.getDownlink(),
      rtt,
      saveData: this.getSaveDataStatus(),
      lastChecked: new Date()
    };

    // 检测状态变化并发出事件
    this.detectStatusChanges(previousStatus, this.currentStatus);
  }

  /**
   * 测试单个URL的连接
   */
  private async testConnection(url: string): Promise<number> {
    const startTime = Date.now();
    
    try {
      await axios.get(url, {
        timeout: this.config.timeoutDuration,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      
      return Date.now() - startTime;
    } catch (error) {
      throw new Error(`连接测试失败: ${url}`);
    }
  }

  /**
   * 获取连接类型
   */
  private getConnectionType(): NetworkStatus['connectionType'] {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection && connection.type) {
        switch (connection.type) {
          case 'wifi':
            return 'wifi';
          case 'cellular':
            return 'cellular';
          case 'ethernet':
            return 'ethernet';
          default:
            return 'unknown';
        }
      }
    }
    return 'unknown';
  }

  /**
   * 获取有效连接类型
   */
  private getEffectiveType(rtt: number): NetworkStatus['effectiveType'] {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection && connection.effectiveType) {
        return connection.effectiveType;
      }
    }

    // 基于RTT估算连接类型
    if (rtt > 2000) return 'slow-2g';
    if (rtt > 1400) return '2g';
    if (rtt > 270) return '3g';
    return '4g';
  }

  /**
   * 获取下行带宽
   */
  private getDownlink(): number {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection && typeof connection.downlink === 'number') {
        return connection.downlink;
      }
    }
    return 0;
  }

  /**
   * 获取数据节省模式状态
   */
  private getSaveDataStatus(): boolean {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection && typeof connection.saveData === 'boolean') {
        return connection.saveData;
      }
    }
    return false;
  }

  /**
   * 检测状态变化并发出相应事件
   */
  private detectStatusChanges(previous: NetworkStatus, current: NetworkStatus): void {
    // 在线状态变化
    if (previous.isOnline !== current.isOnline) {
      if (current.isOnline) {
        this.emit('network-online', {
          type: 'online',
          status: current,
          timestamp: new Date(),
          previousStatus: previous
        } as NetworkEvent);

        // 如果之前离线，现在重新连接
        if (!previous.isOnline) {
          this.emit('network-reconnected', {
            type: 'reconnected',
            status: current,
            timestamp: new Date(),
            previousStatus: previous
          } as NetworkEvent);
        }
      } else {
        this.emit('network-offline', {
          type: 'offline',
          status: current,
          timestamp: new Date(),
          previousStatus: previous
        } as NetworkEvent);
      }
    }

    // 连接速度变化
    if (current.isOnline && previous.isOnline) {
      const currentSpeed = this.categorizeSpeed(current.rtt);
      const previousSpeed = this.categorizeSpeed(previous.rtt);
      
      if (currentSpeed !== previousSpeed) {
        this.emit('network-speed-changed', {
          type: currentSpeed === 'fast' ? 'fast' : 'slow',
          status: current,
          timestamp: new Date(),
          previousStatus: previous
        } as NetworkEvent);
      }
    }

    // 发出状态更新事件
    this.emit('status-updated', current, previous);
  }

  /**
   * 根据RTT分类网络速度
   */
  private categorizeSpeed(rtt: number): 'fast' | 'slow' {
    return rtt < 500 ? 'fast' : 'slow';
  }

  /**
   * 设置浏览器事件监听器
   */
  private setupBrowserEventListeners(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.checkNetworkStatus();
      });

      window.addEventListener('offline', () => {
        this.checkNetworkStatus();
      });

      // 监听连接变化（如果支持）
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        if (connection) {
          connection.addEventListener('change', () => {
            this.checkNetworkStatus();
          });
        }
      }
    }
  }

  /**
   * 检查网络是否稳定
   */
  isNetworkStable(): boolean {
    return this.currentStatus.isOnline && this.consecutiveFailures === 0;
  }

  /**
   * 获取网络质量评分 (0-100)
   */
  getNetworkQuality(): number {
    if (!this.currentStatus.isOnline) {
      return 0;
    }

    let score = 100;
    
    // 基于RTT扣分
    if (this.currentStatus.rtt > 1000) score -= 50;
    else if (this.currentStatus.rtt > 500) score -= 30;
    else if (this.currentStatus.rtt > 200) score -= 15;

    // 基于连续失败次数扣分
    score -= this.consecutiveFailures * 10;

    // 基于连接类型调整
    switch (this.currentStatus.effectiveType) {
      case 'slow-2g':
        score -= 40;
        break;
      case '2g':
        score -= 30;
        break;
      case '3g':
        score -= 15;
        break;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 获取建议的下载策略
   */
  getRecommendedDownloadStrategy(): {
    maxConcurrent: number;
    chunkSize: number;
    retryDelay: number;
  } {
    const quality = this.getNetworkQuality();
    
    if (quality >= 80) {
      return {
        maxConcurrent: 6,
        chunkSize: 1024 * 1024, // 1MB
        retryDelay: 1000
      };
    } else if (quality >= 60) {
      return {
        maxConcurrent: 4,
        chunkSize: 512 * 1024, // 512KB
        retryDelay: 2000
      };
    } else if (quality >= 40) {
      return {
        maxConcurrent: 2,
        chunkSize: 256 * 1024, // 256KB
        retryDelay: 3000
      };
    } else {
      return {
        maxConcurrent: 1,
        chunkSize: 128 * 1024, // 128KB
        retryDelay: 5000
      };
    }
  }
}

export default NetworkMonitor;
