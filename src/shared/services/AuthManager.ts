import { EventEmitter } from 'events';
import {
  LoginCredentials,
  AuthResult,
  UserInfo,
  CaptchaInfo,
  AuthError,
  NetworkError,
  ValidationError
} from '../types';
import { SmartEduClient } from './SmartEduClient';
import { validateLoginCredentials } from '../utils/validation';

/**
 * 认证状态枚举
 */
export enum AuthState {
  GUEST = 'guest',
  LOGGING_IN = 'logging_in',
  LOGGED_IN = 'logged_in',
  LOGGED_OUT = 'logged_out',
  SESSION_EXPIRED = 'session_expired',
  ERROR = 'error'
}

/**
 * 认证事件类型
 */
export interface AuthEvents {
  stateChange: (state: AuthState, user?: UserInfo) => void;
  loginSuccess: (user: UserInfo) => void;
  loginError: (error: string) => void;
  logout: () => void;
  sessionExpired: () => void;
  captchaRequired: (captchaInfo: CaptchaInfo) => void;
}

/**
 * 会话信息
 */
interface SessionInfo {
  token: string;
  user: UserInfo;
  expiresAt: Date;
  refreshToken?: string;
}

/**
 * 认证管理器
 * 负责用户登录、登出、会话管理等认证相关功能
 */
export class AuthManager extends EventEmitter {
  private client: SmartEduClient;
  private currentState: AuthState = AuthState.GUEST;
  private sessionInfo: SessionInfo | null = null;
  private sessionCheckInterval: NodeJS.Timeout | null = null;
  private readonly sessionCheckIntervalMs = 5 * 60 * 1000; // 5分钟检查一次会话状态
  private readonly sessionStorageKey = 'smartedu_session';
  private captchaInfo: CaptchaInfo | null = null;

  constructor(client: SmartEduClient) {
    super();
    this.client = client;
    this.loadSessionFromStorage();
    this.startSessionCheck();
  }

  /**
   * 获取当前认证状态
   */
  getCurrentState(): AuthState {
    return this.currentState;
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    return this.currentState === AuthState.LOGGED_IN && this.sessionInfo !== null;
  }

  /**
   * 检查是否为访客模式
   */
  isGuestMode(): boolean {
    return this.currentState === AuthState.GUEST;
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): UserInfo | null {
    return this.sessionInfo?.user || null;
  }

  /**
   * 获取认证头信息
   */
  async getAuthHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {};
    
    if (this.sessionInfo?.token) {
      headers['Authorization'] = `Bearer ${this.sessionInfo.token}`;
    }
    
    return headers;
  }

  /**
   * 使用凭据登录
   */
  async loginWithCredentials(username: string, password: string, captcha?: string): Promise<AuthResult> {
    try {
      // 验证登录凭据
      const credentials: LoginCredentials = { username, password, captcha };
      const validationResult = validateLoginCredentials(credentials);
      if (!validationResult.isValid) {
        throw new ValidationError(`登录凭据验证失败: ${validationResult.errors.join(', ')}`);
      }

      this.setState(AuthState.LOGGING_IN);

      // 如果需要验证码但未提供，先获取验证码
      if (!captcha && this.captchaInfo) {
        credentials.captcha = captcha;
        credentials.captchaToken = this.captchaInfo.token;
      }

      // 执行登录
      const result = await this.client.login(username, password, captcha);

      if (result.success && result.user && result.token) {
        // 登录成功
        const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 默认24小时过期
        this.sessionInfo = {
          token: result.token,
          user: result.user,
          expiresAt
        };

        this.saveSessionToStorage();
        this.setState(AuthState.LOGGED_IN, result.user);
        this.emit('loginSuccess', result.user);
        this.captchaInfo = null; // 清除验证码信息

        return result;
      } else if (result.requiresCaptcha && result.captchaImage) {
        // 需要验证码
        this.captchaInfo = {
          image: result.captchaImage,
          token: result.captchaImage // 简化处理，实际应该有单独的token
        };
        this.emit('captchaRequired', this.captchaInfo);
        
        return {
          success: false,
          requiresCaptcha: true,
          captchaImage: result.captchaImage,
          error: '需要验证码'
        };
      } else {
        // 登录失败
        this.setState(AuthState.ERROR);
        const error = result.error || '登录失败';
        this.emit('loginError', error);
        throw new AuthError(error);
      }
    } catch (error) {
      this.setState(AuthState.ERROR);
      const errorMessage = error instanceof Error ? error.message : '登录过程中发生未知错误';
      this.emit('loginError', errorMessage);
      throw error;
    }
  }

  /**
   * 访客模式登录
   */
  async loginAsGuest(): Promise<void> {
    try {
      this.setState(AuthState.GUEST);
      this.sessionInfo = null;
      this.clearSessionFromStorage();
      
      // 访客模式不需要实际的网络请求，直接设置状态
      console.log('已切换到访客模式');
    } catch (error) {
      this.setState(AuthState.ERROR);
      throw new AuthError('切换到访客模式失败');
    }
  }

  /**
   * 登出
   */
  async logout(): Promise<void> {
    try {
      // 如果有有效会话，通知服务器登出
      if (this.sessionInfo) {
        try {
          await this.client.logout();
        } catch (error) {
          console.warn('服务器登出请求失败:', error);
          // 即使服务器登出失败，也要清除本地会话
        }
      }

      // 清除本地会话信息
      this.sessionInfo = null;
      this.captchaInfo = null;
      this.clearSessionFromStorage();
      this.setState(AuthState.LOGGED_OUT);
      this.emit('logout');
    } catch (error) {
      throw new AuthError('登出失败');
    }
  }

  /**
   * 刷新令牌
   */
  async refreshToken(): Promise<boolean> {
    try {
      if (!this.sessionInfo?.refreshToken) {
        return false;
      }

      // 检查服务器端会话状态
      const isValid = await this.client.checkAuthStatus();
      
      if (isValid) {
        // 更新过期时间
        this.sessionInfo.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
        this.saveSessionToStorage();
        return true;
      } else {
        // 会话已过期
        await this.handleSessionExpired();
        return false;
      }
    } catch (error) {
      console.error('刷新令牌失败:', error);
      await this.handleSessionExpired();
      return false;
    }
  }

  /**
   * 获取验证码
   */
  async getCaptcha(): Promise<CaptchaInfo> {
    try {
      const captchaInfo = await this.client.getCaptcha();
      this.captchaInfo = captchaInfo;
      return captchaInfo;
    } catch (error) {
      throw new NetworkError('获取验证码失败');
    }
  }

  /**
   * 验证当前会话
   */
  async validateSession(): Promise<boolean> {
    if (!this.sessionInfo) {
      return false;
    }

    // 检查本地过期时间
    if (this.sessionInfo.expiresAt <= new Date()) {
      await this.handleSessionExpired();
      return false;
    }

    // 检查服务器端会话状态
    try {
      const isValid = await this.client.checkAuthStatus();
      if (!isValid) {
        await this.handleSessionExpired();
        return false;
      }
      return true;
    } catch (error) {
      console.error('验证会话失败:', error);
      return false;
    }
  }

  /**
   * 处理会话过期
   */
  private async handleSessionExpired(): Promise<void> {
    this.sessionInfo = null;
    this.clearSessionFromStorage();
    this.setState(AuthState.SESSION_EXPIRED);
    this.emit('sessionExpired');
  }

  /**
   * 设置认证状态
   */
  private setState(state: AuthState, user?: UserInfo): void {
    if (this.currentState !== state) {
      this.currentState = state;
      this.emit('stateChange', state, user);
    }
  }

  /**
   * 从本地存储加载会话信息
   */
  private loadSessionFromStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const stored = localStorage.getItem(this.sessionStorageKey);
        if (stored) {
          const sessionData = JSON.parse(stored);
          
          // 检查会话是否过期
          const expiresAt = new Date(sessionData.expiresAt);
          if (expiresAt > new Date()) {
            this.sessionInfo = {
              ...sessionData,
              expiresAt
            };
            this.setState(AuthState.LOGGED_IN, this.sessionInfo?.user);
          } else {
            // 会话已过期，清除存储
            this.clearSessionFromStorage();
          }
        }
      }
    } catch (error) {
      console.error('加载会话信息失败:', error);
      this.clearSessionFromStorage();
    }
  }

  /**
   * 保存会话信息到本地存储
   */
  private saveSessionToStorage(): void {
    try {
      if (typeof localStorage !== 'undefined' && this.sessionInfo) {
        localStorage.setItem(this.sessionStorageKey, JSON.stringify(this.sessionInfo));
      }
    } catch (error) {
      console.error('保存会话信息失败:', error);
    }
  }

  /**
   * 清除本地存储的会话信息
   */
  private clearSessionFromStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem(this.sessionStorageKey);
      }
    } catch (error) {
      console.error('清除会话信息失败:', error);
    }
  }

  /**
   * 启动会话检查定时器
   */
  private startSessionCheck(): void {
    this.sessionCheckInterval = setInterval(async () => {
      if (this.isLoggedIn()) {
        await this.validateSession();
      }
    }, this.sessionCheckIntervalMs);
  }

  /**
   * 停止会话检查定时器
   */
  private stopSessionCheck(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
  }

  /**
   * 销毁认证管理器
   */
  destroy(): void {
    this.stopSessionCheck();
    this.removeAllListeners();
    this.sessionInfo = null;
    this.captchaInfo = null;
  }

  /**
   * 获取认证状态信息（用于调试）
   */
  getDebugInfo(): {
    state: AuthState;
    hasSession: boolean;
    sessionExpiry: Date | null;
    hasCaptcha: boolean;
    user: UserInfo | null;
  } {
    return {
      state: this.currentState,
      hasSession: this.sessionInfo !== null,
      sessionExpiry: this.sessionInfo?.expiresAt || null,
      hasCaptcha: this.captchaInfo !== null,
      user: this.getCurrentUser()
    };
  }
}

export default AuthManager;