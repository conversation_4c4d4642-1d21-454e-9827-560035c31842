import { EventEmitter } from 'events';
import { NetworkError, ApiError, FileError } from '../types';

/**
 * 重试策略类型
 */
export type RetryStrategy = 'exponential' | 'linear' | 'fixed';

/**
 * 错误类型
 */
export type ErrorType = 'network' | 'api' | 'file' | 'timeout' | 'unknown';

/**
 * 重试配置
 */
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  strategy: RetryStrategy;
  backoffFactor: number;
  jitter: boolean;
  retryableErrors: ErrorType[];
  timeoutMultiplier: number;
}

/**
 * 重试状态
 */
export interface RetryState {
  attempt: number;
  totalDelay: number;
  lastError?: Error;
  nextRetryAt?: Date;
  startTime: Date;
  errorHistory: Array<{
    error: Error;
    timestamp: Date;
    attempt: number;
  }>;
}

/**
 * 重试结果
 */
export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  totalTime: number;
  finalState: RetryState;
}

/**
 * 重试管理器事件
 */
export interface RetryManagerEvents {
  'retry-attempt': (state: RetryState, error: Error) => void;
  'retry-success': (state: RetryState, result: any) => void;
  'retry-failed': (state: RetryState, finalError: Error) => void;
  'retry-exhausted': (state: RetryState) => void;
}

/**
 * 重试管理器
 * 提供智能重试机制，支持多种重试策略和错误类型处理
 */
export class RetryManager extends EventEmitter {
  private config: RetryConfig;

  private readonly defaultConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    strategy: 'exponential',
    backoffFactor: 2,
    jitter: true,
    retryableErrors: ['network', 'timeout', 'api'],
    timeoutMultiplier: 1.5
  };

  constructor(config?: Partial<RetryConfig>) {
    super();
    this.config = { ...this.defaultConfig, ...config };
  }

  /**
   * 执行带重试的操作
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    customConfig?: Partial<RetryConfig>
  ): Promise<RetryResult<T>> {
    const config = { ...this.config, ...customConfig };
    const state: RetryState = {
      attempt: 0,
      totalDelay: 0,
      startTime: new Date(),
      errorHistory: []
    };

    while (state.attempt <= config.maxRetries) {
      try {
        // 如果不是第一次尝试，先等待
        if (state.attempt > 0) {
          const delay = this.calculateDelay(state.attempt, config);
          state.totalDelay += delay;
          state.nextRetryAt = new Date(Date.now() + delay);

          this.emit('retry-attempt', state, state.lastError!);
          
          await this.delay(delay);
        }

        // 执行操作
        const result = await operation();
        
        // 成功
        const totalTime = Date.now() - state.startTime.getTime();
        this.emit('retry-success', state, result);
        
        return {
          success: true,
          result,
          attempts: state.attempt + 1,
          totalTime,
          finalState: state
        };
      } catch (error) {
        state.attempt++;
        state.lastError = error as Error;
        state.errorHistory.push({
          error: error as Error,
          timestamp: new Date(),
          attempt: state.attempt
        });

        // 检查是否应该重试
        if (state.attempt > config.maxRetries || !this.shouldRetry(error as Error, config)) {
          const totalTime = Date.now() - state.startTime.getTime();
          
          if (state.attempt > config.maxRetries) {
            this.emit('retry-exhausted', state);
          } else {
            this.emit('retry-failed', state, error as Error);
          }

          return {
            success: false,
            error: error as Error,
            attempts: state.attempt,
            totalTime,
            finalState: state
          };
        }
      }
    }

    // 这里不应该到达，但为了类型安全
    const totalTime = Date.now() - state.startTime.getTime();
    return {
      success: false,
      error: state.lastError || new Error('未知错误'),
      attempts: state.attempt,
      totalTime,
      finalState: state
    };
  }

  /**
   * 判断错误是否应该重试
   */
  private shouldRetry(error: Error, config: RetryConfig): boolean {
    const errorType = this.classifyError(error);
    return config.retryableErrors.includes(errorType);
  }

  /**
   * 分类错误类型
   */
  private classifyError(error: Error): ErrorType {
    if (error instanceof NetworkError) {
      return 'network';
    }
    
    if (error instanceof ApiError) {
      return 'api';
    }
    
    if (error instanceof FileError) {
      return 'file';
    }

    // 检查错误消息中的关键词
    const message = error.message.toLowerCase();
    
    if (message.includes('timeout') || message.includes('超时')) {
      return 'timeout';
    }
    
    if (message.includes('network') || message.includes('网络') || 
        message.includes('connection') || message.includes('连接')) {
      return 'network';
    }
    
    if (message.includes('api') || message.includes('server') || 
        message.includes('服务器') || message.includes('http')) {
      return 'api';
    }
    
    if (message.includes('file') || message.includes('文件') || 
        message.includes('path') || message.includes('路径')) {
      return 'file';
    }

    return 'unknown';
  }

  /**
   * 计算延迟时间
   */
  private calculateDelay(attempt: number, config: RetryConfig): number {
    let delay: number;

    switch (config.strategy) {
      case 'exponential':
        delay = config.baseDelay * Math.pow(config.backoffFactor, attempt - 1);
        break;
      
      case 'linear':
        delay = config.baseDelay * attempt;
        break;
      
      case 'fixed':
      default:
        delay = config.baseDelay;
        break;
    }

    // 应用最大延迟限制
    delay = Math.min(delay, config.maxDelay);

    // 添加抖动以避免雷群效应
    if (config.jitter) {
      const jitterRange = delay * 0.1; // 10% 抖动
      const jitter = (Math.random() - 0.5) * 2 * jitterRange;
      delay += jitter;
    }

    return Math.max(delay, 0);
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建带重试的函数包装器
   */
  wrap<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    customConfig?: Partial<RetryConfig>
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      const result = await this.executeWithRetry(() => fn(...args), customConfig);
      
      if (result.success) {
        return result.result!;
      } else {
        throw result.error;
      }
    };
  }

  /**
   * 批量重试操作
   */
  async executeBatchWithRetry<T>(
    operations: Array<() => Promise<T>>,
    customConfig?: Partial<RetryConfig>
  ): Promise<Array<RetryResult<T>>> {
    const results: Array<RetryResult<T>> = [];
    
    for (const operation of operations) {
      const result = await this.executeWithRetry(operation, customConfig);
      results.push(result);
    }
    
    return results;
  }

  /**
   * 并行批量重试操作
   */
  async executeBatchParallelWithRetry<T>(
    operations: Array<() => Promise<T>>,
    customConfig?: Partial<RetryConfig>,
    concurrency: number = 3
  ): Promise<Array<RetryResult<T>>> {
    const results: Array<RetryResult<T>> = [];
    const executing: Array<Promise<void>> = [];

    for (let i = 0; i < operations.length; i++) {
      const operation = operations[i];
      
      const promise = this.executeWithRetry(operation, customConfig)
        .then(result => {
          results[i] = result;
        });

      executing.push(promise);

      // 控制并发数
      if (executing.length >= concurrency) {
        await Promise.race(executing);
        // 移除已完成的 promise
        const completedIndex = executing.findIndex(p => 
          p === promise || (p as any).isResolved
        );
        if (completedIndex !== -1) {
          executing.splice(completedIndex, 1);
        }
      }
    }

    // 等待所有剩余的操作完成
    await Promise.all(executing);
    
    return results;
  }

  /**
   * 获取错误统计信息
   */
  getErrorStats(states: RetryState[]): {
    totalAttempts: number;
    totalErrors: number;
    errorsByType: Record<ErrorType, number>;
    averageAttempts: number;
    successRate: number;
  } {
    const totalStates = states.length;
    const totalAttempts = states.reduce((sum, state) => sum + state.attempt, 0);
    const totalErrors = states.reduce((sum, state) => sum + state.errorHistory.length, 0);
    
    const errorsByType: Record<ErrorType, number> = {
      network: 0,
      api: 0,
      file: 0,
      timeout: 0,
      unknown: 0
    };

    states.forEach(state => {
      state.errorHistory.forEach(errorRecord => {
        const errorType = this.classifyError(errorRecord.error);
        errorsByType[errorType]++;
      });
    });

    const successfulStates = states.filter(state => !state.lastError);
    const successRate = totalStates > 0 ? successfulStates.length / totalStates : 0;
    const averageAttempts = totalStates > 0 ? totalAttempts / totalStates : 0;

    return {
      totalAttempts,
      totalErrors,
      errorsByType,
      averageAttempts,
      successRate
    };
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<RetryConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): RetryConfig {
    return { ...this.config };
  }

  /**
   * 重置配置为默认值
   */
  resetConfig(): void {
    this.config = { ...this.defaultConfig };
  }

  /**
   * 创建预设配置
   */
  static createPresetConfig(preset: 'aggressive' | 'conservative' | 'fast' | 'robust'): RetryConfig {
    const baseConfig: RetryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      strategy: 'exponential',
      backoffFactor: 2,
      jitter: true,
      retryableErrors: ['network', 'timeout', 'api'],
      timeoutMultiplier: 1.5
    };

    switch (preset) {
      case 'aggressive':
        return {
          ...baseConfig,
          maxRetries: 5,
          baseDelay: 500,
          maxDelay: 60000,
          backoffFactor: 1.5
        };
      
      case 'conservative':
        return {
          ...baseConfig,
          maxRetries: 2,
          baseDelay: 2000,
          maxDelay: 15000,
          backoffFactor: 3
        };
      
      case 'fast':
        return {
          ...baseConfig,
          maxRetries: 3,
          baseDelay: 200,
          maxDelay: 5000,
          strategy: 'linear',
          backoffFactor: 1
        };
      
      case 'robust':
        return {
          ...baseConfig,
          maxRetries: 7,
          baseDelay: 1000,
          maxDelay: 120000,
          backoffFactor: 2.5,
          retryableErrors: ['network', 'timeout', 'api', 'file']
        };
      
      default:
        return baseConfig;
    }
  }
}

export default RetryManager;
