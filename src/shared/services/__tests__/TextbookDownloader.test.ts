import axios from 'axios';
import * as fs from 'fs-extra';
import { TextbookDownloader, TextbookDownloaderConfig } from '../TextbookDownloader';
import { FileOrganizer, FileOrganizerConfig } from '../FileOrganizer';
import { CourseResource } from '../../types';

// Mock dependencies
jest.mock('axios');
jest.mock('fs-extra');

const mockAxios = axios as jest.Mocked<typeof axios>;
const mockFs = fs as jest.Mocked<typeof fs>;

describe('TextbookDownloader', () => {
  let downloader: TextbookDownloader;
  let fileOrganizer: FileOrganizer;
  let mockResource: CourseResource;
  let config: TextbookDownloaderConfig;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock FileOrganizer
    const organizerConfig: FileOrganizerConfig = {
      basePath: '/test/downloads',
      namingPattern: '{stage}-{grade}-{subject}-{title}',
      createSubfolders: true,
      groupBySubject: true,
      groupByGrade: true
    };

    fileOrganizer = new FileOrganizer(organizerConfig);
    
    // Mock FileOrganizer methods
    jest.spyOn(fileOrganizer, 'generatePath').mockReturnValue('/test/downloads/math/grade3/textbook.pdf');
    jest.spyOn(fileOrganizer, 'checkVersionUpdate').mockResolvedValue(true);
    jest.spyOn(fileOrganizer, 'createDirectoryStructure').mockResolvedValue();
    jest.spyOn(fileOrganizer, 'registerFile').mockResolvedValue();

    config = {
      maxConcurrentDownloads: 3,
      chunkSize: 1024 * 1024,
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000
    };

    downloader = new TextbookDownloader(fileOrganizer, config);

    mockResource = {
      id: 'textbook-123',
      title: '数学教材',
      type: 'textbook',
      url: 'https://example.com/textbook.pdf',
      metadata: {
        stage: '小学',
        grade: '三年级',
        subject: '数学',
        version: '人教版',
        volume: '上册',
        fileSize: 1024000
      },
      requiresAuth: false,
      accessLevel: 'public'
    };

    // Mock fs methods
    mockFs.pathExists.mockResolvedValue(true);
    mockFs.stat.mockResolvedValue({ size: 1024000 } as any);
    mockFs.createWriteStream.mockReturnValue({
      write: jest.fn(),
      end: jest.fn(),
      destroy: jest.fn(),
      on: jest.fn(),
      pipe: jest.fn()
    } as any);
    mockFs.move.mockResolvedValue();
    mockFs.remove.mockResolvedValue();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('downloadTextbook', () => {
    it('should successfully download a textbook', async () => {
      // Mock axios responses
      mockAxios.head.mockResolvedValue({
        headers: {
          'content-length': '1024000',
          'accept-ranges': 'bytes'
        }
      } as any);

      mockAxios.get.mockResolvedValue({
        data: {
          pipe: jest.fn(),
          on: jest.fn((event, callback) => {
            if (event === 'end') {
              setTimeout(callback, 10);
            }
          })
        }
      } as any);

      const progressCallback = jest.fn();
      const result = await downloader.downloadTextbook(mockResource, progressCallback);

      expect(result.status).toBe('completed');
      expect(result.progress).toBe(100);
      expect(fileOrganizer.generatePath).toHaveBeenCalledWith(mockResource);
      expect(fileOrganizer.checkVersionUpdate).toHaveBeenCalled();
      expect(fileOrganizer.createDirectoryStructure).toHaveBeenCalled();
      expect(fileOrganizer.registerFile).toHaveBeenCalled();
      expect(progressCallback).toHaveBeenCalled();
    });

    it('should handle download failure with retry', async () => {
      // Mock axios to fail first two times, then succeed
      mockAxios.head
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          headers: {
            'content-length': '1024000',
            'accept-ranges': 'bytes'
          }
        } as any);

      mockAxios.get.mockResolvedValue({
        data: {
          pipe: jest.fn(),
          on: jest.fn((event, callback) => {
            if (event === 'end') {
              setTimeout(callback, 10);
            }
          })
        }
      } as any);

      const result = await downloader.downloadTextbook(mockResource);

      expect(result.status).toBe('completed');
      expect(mockAxios.head).toHaveBeenCalledTimes(3);
    });

    it('should fail after max retry attempts', async () => {
      // Mock axios to always fail
      mockAxios.head.mockRejectedValue(new Error('Persistent network error'));

      await expect(downloader.downloadTextbook(mockResource)).rejects.toThrow();
    });

    it('should skip download if file already exists and is up to date', async () => {
      // Mock file organizer to indicate no update needed
      jest.spyOn(fileOrganizer, 'checkVersionUpdate').mockResolvedValue(false);

      const result = await downloader.downloadTextbook(mockResource);

      expect(result.status).toBe('completed');
      expect(result.progress).toBe(100);
      expect(mockAxios.head).not.toHaveBeenCalled();
      expect(mockAxios.get).not.toHaveBeenCalled();
    });

    it('should handle chunked download for large files', async () => {
      const largeFileSize = 10 * 1024 * 1024; // 10MB
      
      mockAxios.head.mockResolvedValue({
        headers: {
          'content-length': largeFileSize.toString(),
          'accept-ranges': 'bytes'
        }
      } as any);

      // Mock chunked responses
      mockAxios.get.mockImplementation((url, options) => {
        const range = options?.headers?.Range;
        if (range) {
          return Promise.resolve({
            data: {
              pipe: jest.fn(),
              on: jest.fn((event, callback) => {
                if (event === 'end') {
                  setTimeout(callback, 10);
                }
              })
            }
          });
        }
        return Promise.reject(new Error('Unexpected request'));
      });

      const progressCallback = jest.fn();
      const result = await downloader.downloadTextbook(mockResource, progressCallback);

      expect(result.status).toBe('completed');
      expect(mockAxios.get).toHaveBeenCalledTimes(Math.ceil(largeFileSize / config.chunkSize));
      expect(progressCallback).toHaveBeenCalled();
    });

    it('should validate downloaded file', async () => {
      mockAxios.head.mockResolvedValue({
        headers: {
          'content-length': '1024000'
        }
      } as any);

      mockAxios.get.mockResolvedValue({
        data: {
          pipe: jest.fn(),
          on: jest.fn((event, callback) => {
            if (event === 'end') {
              setTimeout(callback, 10);
            }
          })
        }
      } as any);

      // Mock file validation to fail
      mockFs.pathExists.mockResolvedValueOnce(true).mockResolvedValueOnce(false);

      await expect(downloader.downloadTextbook(mockResource)).rejects.toThrow('下载的文件不存在');
    });

    it('should handle file size mismatch during validation', async () => {
      mockAxios.head.mockResolvedValue({
        headers: {
          'content-length': '1024000'
        }
      } as any);

      mockAxios.get.mockResolvedValue({
        data: {
          pipe: jest.fn(),
          on: jest.fn((event, callback) => {
            if (event === 'end') {
              setTimeout(callback, 10);
            }
          })
        }
      } as any);

      // Mock file stat to return different size
      mockFs.stat.mockResolvedValue({ size: 512000 } as any);

      await expect(downloader.downloadTextbook(mockResource)).rejects.toThrow('文件大小不匹配');
    });
  });

  describe('cancelDownload', () => {
    it('should cancel an active download', async () => {
      // Start a download
      const downloadPromise = downloader.downloadTextbook(mockResource);
      
      // Get the task ID (this is a bit tricky to test without exposing internals)
      const activeTasks = downloader.getActiveTasks();
      expect(activeTasks.length).toBe(1);
      
      const taskId = activeTasks[0].id;
      
      // Cancel the download
      downloader.cancelDownload(taskId);
      
      const task = downloader.getTaskStatus(taskId);
      expect(task?.status).toBe('cancelled');
    });
  });

  describe('getActiveTasks', () => {
    it('should return list of active tasks', () => {
      const tasks = downloader.getActiveTasks();
      expect(Array.isArray(tasks)).toBe(true);
    });
  });

  describe('clearCompletedTasks', () => {
    it('should clear completed tasks', () => {
      downloader.clearCompletedTasks();
      // This method doesn't return anything, so we just ensure it doesn't throw
      expect(true).toBe(true);
    });
  });

  describe('updateConfig', () => {
    it('should update configuration', () => {
      const newConfig = { maxConcurrentDownloads: 5 };
      downloader.updateConfig(newConfig);
      
      const currentConfig = downloader.getConfig();
      expect(currentConfig.maxConcurrentDownloads).toBe(5);
    });
  });

  describe('getConfig', () => {
    it('should return current configuration', () => {
      const currentConfig = downloader.getConfig();
      expect(currentConfig).toEqual(config);
    });
  });
});
