import axios from 'axios';
import * as fs from 'fs-extra';
import * as path from 'path';
import { TextbookDownloader } from '../TextbookDownloader';
import { DownloadManager } from '../DownloadManager';
import { FileOrganizer } from '../FileOrganizer';
import { CourseResource } from '../../types';

// Mock dependencies for integration test
jest.mock('axios');
jest.mock('fs-extra');

const mockAxios = axios as jest.Mocked<typeof axios>;
const mockFs = fs as jest.Mocked<typeof fs>;

describe('Textbook Download Integration', () => {
  let downloadManager: DownloadManager;
  let fileOrganizer: FileOrganizer;
  let mockResource: CourseResource;
  let tempDir: string;

  beforeEach(() => {
    jest.clearAllMocks();

    tempDir = '/tmp/test-downloads';

    // Setup FileOrganizer
    fileOrganizer = new FileOrganizer({
      basePath: tempDir,
      namingPattern: '{stage}-{grade}-{subject}-{title}',
      createSubfolders: true,
      groupBySubject: true,
      groupByGrade: true
    });

    // Setup DownloadManager
    downloadManager = new DownloadManager(fileOrganizer, {
      maxConcurrentDownloads: 2,
      autoRetry: true,
      maxRetries: 3,
      retryDelay: 100, // Faster for testing
      enableNotifications: false
    });

    mockResource = {
      id: 'integration-test-resource',
      title: '数学基础教材',
      type: 'textbook',
      url: 'https://example.com/math-textbook.pdf',
      metadata: {
        stage: '小学',
        grade: '三年级',
        subject: '数学',
        version: '人教版',
        volume: '上册',
        fileSize: 2048000 // 2MB
      },
      requiresAuth: false,
      accessLevel: 'public'
    };

    // Setup common mocks
    mockFs.pathExists.mockResolvedValue(false); // File doesn't exist initially
    mockFs.ensureDir.mockResolvedValue();
    mockFs.move.mockResolvedValue();
    mockFs.remove.mockResolvedValue();
    mockFs.stat.mockResolvedValue({ size: mockResource.metadata!.fileSize } as any);
  });

  afterEach(() => {
    downloadManager.destroy();
  });

  describe('Complete Download Flow', () => {
    it('should successfully download a textbook from start to finish', async () => {
      // Mock successful HTTP responses
      mockAxios.head.mockResolvedValue({
        headers: {
          'content-length': mockResource.metadata!.fileSize!.toString(),
          'accept-ranges': 'bytes'
        }
      } as any);

      // Mock file stream for download
      const mockStream = {
        pipe: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === 'end') {
            setTimeout(callback, 50); // Simulate download time
          }
          if (event === 'error') {
            // No error for successful case
          }
        })
      };

      mockAxios.get.mockResolvedValue({
        data: mockStream
      } as any);

      // Mock write stream
      const mockWriteStream = {
        write: jest.fn(),
        end: jest.fn(),
        destroy: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === 'finish') {
            setTimeout(callback, 10);
          }
        }),
        pipe: jest.fn()
      };

      mockFs.createWriteStream.mockReturnValue(mockWriteStream as any);

      // Track events
      const events: string[] = [];
      downloadManager.on('task-added', () => events.push('task-added'));
      downloadManager.on('task-started', () => events.push('task-started'));
      downloadManager.on('task-completed', () => events.push('task-completed'));

      // Add task and wait for completion
      const task = downloadManager.addTask(mockResource);
      
      // Wait for task to complete (with timeout)
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Test timeout'));
        }, 5000);

        downloadManager.on('task-completed', (completedTask) => {
          if (completedTask.id === task.id) {
            clearTimeout(timeout);
            resolve();
          }
        });

        downloadManager.on('task-failed', (failedTask, error) => {
          if (failedTask.id === task.id) {
            clearTimeout(timeout);
            reject(new Error(`Task failed: ${error}`));
          }
        });
      });

      // Verify the download process
      expect(events).toContain('task-added');
      expect(events).toContain('task-started');
      expect(events).toContain('task-completed');

      // Verify task final state
      const finalTask = downloadManager.getTask(task.id);
      expect(finalTask?.status).toBe('completed');
      expect(finalTask?.progress).toBe(100);

      // Verify file operations were called
      expect(mockFs.ensureDir).toHaveBeenCalled();
      expect(mockFs.createWriteStream).toHaveBeenCalled();
      expect(mockFs.move).toHaveBeenCalled();

      // Verify HTTP requests
      expect(mockAxios.head).toHaveBeenCalledWith(mockResource.url, expect.any(Object));
      expect(mockAxios.get).toHaveBeenCalled();
    });

    it('should handle download failure and retry', async () => {
      // Mock HTTP failure then success
      mockAxios.head
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockResolvedValueOnce({
          headers: {
            'content-length': mockResource.metadata!.fileSize!.toString(),
            'accept-ranges': 'bytes'
          }
        } as any);

      const mockStream = {
        pipe: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === 'end') {
            setTimeout(callback, 50);
          }
        })
      };

      mockAxios.get.mockResolvedValue({
        data: mockStream
      } as any);

      const mockWriteStream = {
        write: jest.fn(),
        end: jest.fn(),
        destroy: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === 'finish') {
            setTimeout(callback, 10);
          }
        }),
        pipe: jest.fn()
      };

      mockFs.createWriteStream.mockReturnValue(mockWriteStream as any);

      // Add task
      const task = downloadManager.addTask(mockResource);

      // Wait for completion
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Test timeout'));
        }, 10000);

        downloadManager.on('task-completed', (completedTask) => {
          if (completedTask.id === task.id) {
            clearTimeout(timeout);
            resolve();
          }
        });

        downloadManager.on('task-failed', (failedTask, error) => {
          if (failedTask.id === task.id) {
            clearTimeout(timeout);
            reject(new Error(`Task failed: ${error}`));
          }
        });
      });

      // Verify retry attempts were made
      expect(mockAxios.head).toHaveBeenCalledTimes(3);
      
      // Verify final success
      const finalTask = downloadManager.getTask(task.id);
      expect(finalTask?.status).toBe('completed');
    });

    it('should handle batch download', async () => {
      const resources = [
        mockResource,
        {
          ...mockResource,
          id: 'resource-2',
          title: '语文教材',
          url: 'https://example.com/chinese-textbook.pdf'
        },
        {
          ...mockResource,
          id: 'resource-3',
          title: '英语教材',
          url: 'https://example.com/english-textbook.pdf'
        }
      ];

      // Mock successful responses for all resources
      mockAxios.head.mockResolvedValue({
        headers: {
          'content-length': mockResource.metadata!.fileSize!.toString(),
          'accept-ranges': 'bytes'
        }
      } as any);

      const mockStream = {
        pipe: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === 'end') {
            setTimeout(callback, 50);
          }
        })
      };

      mockAxios.get.mockResolvedValue({
        data: mockStream
      } as any);

      const mockWriteStream = {
        write: jest.fn(),
        end: jest.fn(),
        destroy: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === 'finish') {
            setTimeout(callback, 10);
          }
        }),
        pipe: jest.fn()
      };

      mockFs.createWriteStream.mockReturnValue(mockWriteStream as any);

      // Add batch tasks
      const tasks = downloadManager.addBatchTasks(resources);
      expect(tasks).toHaveLength(3);

      // Wait for all tasks to complete
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Batch download timeout'));
        }, 15000);

        let completedCount = 0;
        downloadManager.on('task-completed', () => {
          completedCount++;
          if (completedCount === 3) {
            clearTimeout(timeout);
            resolve();
          }
        });

        downloadManager.on('task-failed', (failedTask, error) => {
          clearTimeout(timeout);
          reject(new Error(`Batch task failed: ${error}`));
        });
      });

      // Verify all tasks completed
      const stats = downloadManager.getStats();
      expect(stats.completedTasks).toBe(3);
      expect(stats.failedTasks).toBe(0);
    });

    it('should handle concurrent download limits', async () => {
      const resources = Array.from({ length: 5 }, (_, i) => ({
        ...mockResource,
        id: `resource-${i}`,
        title: `教材 ${i + 1}`
      }));

      // Mock responses
      mockAxios.head.mockResolvedValue({
        headers: {
          'content-length': mockResource.metadata!.fileSize!.toString(),
          'accept-ranges': 'bytes'
        }
      } as any);

      const mockStream = {
        pipe: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === 'end') {
            setTimeout(callback, 100); // Longer delay to test concurrency
          }
        })
      };

      mockAxios.get.mockResolvedValue({
        data: mockStream
      } as any);

      const mockWriteStream = {
        write: jest.fn(),
        end: jest.fn(),
        destroy: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === 'finish') {
            setTimeout(callback, 10);
          }
        }),
        pipe: jest.fn()
      };

      mockFs.createWriteStream.mockReturnValue(mockWriteStream as any);

      // Track concurrent downloads
      let maxConcurrent = 0;
      let currentConcurrent = 0;

      downloadManager.on('task-started', () => {
        currentConcurrent++;
        maxConcurrent = Math.max(maxConcurrent, currentConcurrent);
      });

      downloadManager.on('task-completed', () => {
        currentConcurrent--;
      });

      // Add all tasks
      const tasks = downloadManager.addBatchTasks(resources);

      // Wait for all to complete
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Concurrent download test timeout'));
        }, 20000);

        let completedCount = 0;
        downloadManager.on('task-completed', () => {
          completedCount++;
          if (completedCount === 5) {
            clearTimeout(timeout);
            resolve();
          }
        });
      });

      // Verify concurrency limit was respected
      expect(maxConcurrent).toBeLessThanOrEqual(2); // Our configured limit
      
      // Verify all completed
      const stats = downloadManager.getStats();
      expect(stats.completedTasks).toBe(5);
    });
  });
});
