import { VideoMerger, MergeProgress } from '../VideoMerger';
import { FFmpegError, FileError } from '../../types';
import * as fs from 'fs-extra';
import * as path from 'path';

// Mock fs-extra
jest.mock('fs-extra');
const mockedFs = fs as jest.Mocked<typeof fs>;

// Mock fluent-ffmpeg
jest.mock('fluent-ffmpeg', () => {
  const mockCommand = {
    input: jest.fn().mockReturnThis(),
    inputOptions: jest.fn().mockReturnThis(),
    videoCodec: jest.fn().mockReturnThis(),
    audioCodec: jest.fn().mockReturnThis(),
    format: jest.fn().mockReturnThis(),
    output: jest.fn().mockReturnThis(),
    videoBitrate: jest.fn().mockReturnThis(),
    audioBitrate: jest.fn().mockReturnThis(),
    on: jest.fn().mockReturnThis(),
    run: jest.fn()
  };

  return jest.fn(() => mockCommand);
});

// Mock @ffmpeg-installer/ffmpeg
jest.mock('@ffmpeg-installer/ffmpeg', () => ({
  path: '/mock/ffmpeg/path'
}));

describe('VideoMerger', () => {
  let merger: VideoMerger;
  let mockFfmpegCommand: any;

  beforeEach(() => {
    merger = new VideoMerger();
    jest.clearAllMocks();
    
    // Reset fs mocks
    mockedFs.pathExists.mockResolvedValue(true);
    mockedFs.stat.mockResolvedValue({ size: 1024 } as any);
    mockedFs.ensureDir.mockResolvedValue(undefined);
    mockedFs.writeFileSync.mockImplementation(() => {});
    mockedFs.remove.mockResolvedValue(undefined);

    // Get the mocked ffmpeg command
    const ffmpeg = require('fluent-ffmpeg');
    mockFfmpegCommand = ffmpeg();
  });

  describe('mergeSegments', () => {
    const mockSegmentPaths = [
      '/temp/segment_000001.ts',
      '/temp/segment_000002.ts',
      '/temp/segment_000003.ts'
    ];
    const mockOutputPath = '/output/merged_video.mp4';

    it('应该成功合并视频片段', async () => {
      // Mock successful merge
      mockFfmpegCommand.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'progress') {
          // Simulate progress events
          setTimeout(() => callback({ percent: 50, timemark: '00:00:30', currentKbps: 1000, currentFps: 30 }), 10);
          setTimeout(() => callback({ percent: 100, timemark: '00:01:00', currentKbps: 1000, currentFps: 30 }), 20);
        } else if (event === 'end') {
          setTimeout(callback, 30);
        }
        return mockFfmpegCommand;
      });

      const progressEvents: MergeProgress[] = [];
      merger.on('progress', (progress: MergeProgress) => {
        progressEvents.push(progress);
      });

      const completedPromise = new Promise<void>((resolve) => {
        merger.on('completed', resolve);
      });

      const mergePromise = merger.mergeSegments(mockSegmentPaths, mockOutputPath);

      await completedPromise;
      await mergePromise;

      expect(mockFfmpegCommand.input).toHaveBeenCalled();
      expect(mockFfmpegCommand.videoCodec).toHaveBeenCalledWith('libx264');
      expect(mockFfmpegCommand.audioCodec).toHaveBeenCalledWith('aac');
      expect(mockFfmpegCommand.format).toHaveBeenCalledWith('mp4');
      expect(mockFfmpegCommand.run).toHaveBeenCalled();

      expect(progressEvents.length).toBeGreaterThan(0);
      expect(progressEvents[0].phase).toBe('preparing');
      expect(progressEvents[progressEvents.length - 1].phase).toBe('completed');
    });

    it('应该在FFmpeg错误时抛出异常', async () => {
      const mockError = new Error('FFmpeg processing failed');
      
      mockFfmpegCommand.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'error') {
          setTimeout(() => callback(mockError), 10);
        }
        return mockFfmpegCommand;
      });

      const errorPromise = new Promise<Error>((resolve) => {
        merger.on('error', resolve);
      });

      const mergePromise = merger.mergeSegments(mockSegmentPaths, mockOutputPath);

      const error = await errorPromise;
      await expect(mergePromise).rejects.toThrow();

      expect(error).toBeInstanceOf(FFmpegError);
      expect(error.message).toContain('视频合并失败');
    });

    it('应该验证输入片段文件', async () => {
      mockedFs.pathExists.mockResolvedValueOnce(false); // First file doesn't exist

      await expect(merger.mergeSegments(mockSegmentPaths, mockOutputPath))
        .rejects.toThrow(FileError);
    });

    it('应该检查片段文件大小', async () => {
      mockedFs.stat.mockResolvedValueOnce({ size: 0 } as any); // First file is empty

      await expect(merger.mergeSegments(mockSegmentPaths, mockOutputPath))
        .rejects.toThrow(FileError);
    });

    it('应该创建输出目录', async () => {
      mockFfmpegCommand.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'end') {
          setTimeout(callback, 10);
        }
        return mockFfmpegCommand;
      });

      await merger.mergeSegments(mockSegmentPaths, mockOutputPath);

      expect(mockedFs.ensureDir).toHaveBeenCalledWith(path.dirname(mockOutputPath));
    });

    it('应该应用质量设置', async () => {
      const highQualityMerger = new VideoMerger({
        quality: 'high'
      });

      mockFfmpegCommand.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'end') {
          setTimeout(callback, 10);
        }
        return mockFfmpegCommand;
      });

      await highQualityMerger.mergeSegments(mockSegmentPaths, mockOutputPath);

      expect(mockFfmpegCommand.videoBitrate).toHaveBeenCalledWith('2000k');
      expect(mockFfmpegCommand.audioBitrate).toHaveBeenCalledWith('128k');
    });

    it('应该处理自定义合并选项', async () => {
      mockFfmpegCommand.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'end') {
          setTimeout(callback, 10);
        }
        return mockFfmpegCommand;
      });

      await merger.mergeSegments(mockSegmentPaths, mockOutputPath, {
        format: 'avi',
        quality: 'low',
        deleteSegments: false
      });

      expect(mockFfmpegCommand.format).toHaveBeenCalledWith('avi');
    });

    it('应该在合并完成后清理片段文件', async () => {
      mockFfmpegCommand.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'end') {
          setTimeout(callback, 10);
        }
        return mockFfmpegCommand;
      });

      await merger.mergeSegments(mockSegmentPaths, mockOutputPath, {
        deleteSegments: true
      });

      // Should attempt to remove each segment file
      expect(mockedFs.remove).toHaveBeenCalledTimes(mockSegmentPaths.length + 1); // +1 for temp dir
    });

    it('应该处理空的片段列表', async () => {
      await expect(merger.mergeSegments([], mockOutputPath))
        .rejects.toThrow(FileError);
    });
  });

  describe('getVideoInfo', () => {
    it('应该获取视频信息', async () => {
      const mockMetadata = {
        format: {
          duration: 120,
          bit_rate: '1000000'
        },
        streams: [
          {
            codec_type: 'video',
            codec_name: 'h264',
            width: 1920,
            height: 1080
          }
        ]
      };

      // Mock ffprobe
      const ffmpeg = require('fluent-ffmpeg');
      ffmpeg.ffprobe = jest.fn((filePath, callback) => {
        callback(null, mockMetadata);
      });

      const result = await merger.getVideoInfo('/path/to/video.mp4');

      expect(result).toEqual(mockMetadata);
      expect(ffmpeg.ffprobe).toHaveBeenCalledWith('/path/to/video.mp4', expect.any(Function));
    });

    it('应该在ffprobe错误时抛出异常', async () => {
      const mockError = new Error('ffprobe failed');

      const ffmpeg = require('fluent-ffmpeg');
      ffmpeg.ffprobe = jest.fn((filePath, callback) => {
        callback(mockError, null);
      });

      await expect(merger.getVideoInfo('/path/to/video.mp4'))
        .rejects.toThrow(FFmpegError);
    });
  });

  describe('配置管理', () => {
    it('应该使用默认配置', () => {
      const defaultMerger = new VideoMerger();
      expect(defaultMerger).toBeInstanceOf(VideoMerger);
    });

    it('应该使用自定义配置', () => {
      const customMerger = new VideoMerger({
        tempDir: '/custom/temp',
        deleteSegments: false,
        outputFormat: 'avi',
        videoCodec: 'libx265',
        audioCodec: 'mp3',
        quality: 'high'
      });

      expect(customMerger).toBeInstanceOf(VideoMerger);
    });

    it('应该更新配置', () => {
      merger.updateConfig({
        quality: 'high',
        deleteSegments: false
      });

      expect(merger).toBeInstanceOf(VideoMerger);
    });
  });

  describe('事件处理', () => {
    it('应该发出进度事件', (done) => {
      mockFfmpegCommand.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'progress') {
          setTimeout(() => callback({ percent: 50 }), 10);
        } else if (event === 'end') {
          setTimeout(callback, 20);
        }
        return mockFfmpegCommand;
      });

      merger.on('progress', (progress: MergeProgress) => {
        expect(progress.phase).toBe('merging');
        expect(progress.progress).toBe(50);
        done();
      });

      merger.mergeSegments(['/test/segment.ts'], '/test/output.mp4');
    });

    it('应该发出完成事件', (done) => {
      mockFfmpegCommand.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'end') {
          setTimeout(callback, 10);
        }
        return mockFfmpegCommand;
      });

      merger.on('completed', (outputPath: string) => {
        expect(outputPath).toBe('/test/output.mp4');
        done();
      });

      merger.mergeSegments(['/test/segment.ts'], '/test/output.mp4');
    });
  });
});
