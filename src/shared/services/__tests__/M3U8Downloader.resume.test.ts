import * as fs from 'fs-extra';
import * as path from 'path';
import { M3U8Downloader } from '../M3U8Downloader';
import { FileOrganizer } from '../FileOrganizer';
import { CourseResource, M3U8Playlist, M3U8Segment } from '../../types';
import axios from 'axios';

// Mock dependencies
jest.mock('axios');
jest.mock('../M3U8Parser');
jest.mock('../VideoMerger');

const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('M3U8Downloader - 断点续传功能', () => {
  let downloader: M3U8Downloader;
  let fileOrganizer: FileOrganizer;
  let testTempDir: string;
  let testStateDir: string;
  let mockResource: CourseResource;
  let mockPlaylist: M3U8Playlist;

  beforeEach(async () => {
    // 创建测试目录
    testTempDir = path.join(__dirname, 'test-temp');
    testStateDir = path.join(__dirname, 'test-states');
    await fs.ensureDir(testTempDir);
    await fs.ensureDir(testStateDir);

    // 创建文件组织器
    fileOrganizer = new FileOrganizer({
      basePath: testTempDir,
      namingPattern: '{title}',
      createSubfolders: false
    });

    // 创建下载器
    downloader = new M3U8Downloader(fileOrganizer, {
      tempDir: testTempDir,
      enableResume: true,
      resumeStateDir: testStateDir,
      networkMonitoring: false, // 在测试中禁用网络监控
      maxConcurrentDownloads: 2,
      deleteSegmentsAfterMerge: false
    });

    // 模拟资源
    mockResource = {
      id: 'test-video-resume',
      title: '断点续传测试视频',
      type: 'video',
      url: 'https://example.com/test.m3u8',
      metadata: {
        stage: '小学',
        grade: '三年级',
        subject: '数学',
        version: '人教版',
        volume: '上册'
      },
      requiresAuth: false
    };

    // 模拟M3U8播放列表
    mockPlaylist = {
      baseUrl: 'https://example.com/',
      segments: [
        { url: 'https://example.com/segment0.ts', duration: 10, sequence: 0 },
        { url: 'https://example.com/segment1.ts', duration: 10, sequence: 1 },
        { url: 'https://example.com/segment2.ts', duration: 10, sequence: 2 },
        { url: 'https://example.com/segment3.ts', duration: 10, sequence: 3 },
        { url: 'https://example.com/segment4.ts', duration: 10, sequence: 4 }
      ],
      totalDuration: 50
    };

    // Mock M3U8Parser
    const mockParser = {
      parsePlaylist: jest.fn().mockResolvedValue(mockPlaylist),
      isM3U8Url: jest.fn().mockReturnValue(true)
    };
    (downloader as any).parser = mockParser;

    // Mock VideoMerger
    const mockMerger = {
      mergeSegments: jest.fn().mockImplementation((segments, output, options) => {
        // 模拟合并过程
        setTimeout(() => {
          mockMerger.emit('progress', { progress: 50 });
          setTimeout(() => {
            mockMerger.emit('progress', { progress: 100 });
            mockMerger.emit('completed');
          }, 100);
        }, 50);
      }),
      on: jest.fn(),
      emit: jest.fn()
    };
    (downloader as any).merger = mockMerger;
  });

  afterEach(async () => {
    // 清理测试目录
    await fs.remove(testTempDir);
    await fs.remove(testStateDir);
  });

  describe('断点续传状态管理', () => {
    it('应该在下载开始时保存断点续传状态', async () => {
      // Mock axios responses
      mockedAxios.get.mockImplementation((url) => {
        if (url.includes('segment')) {
          return Promise.resolve({
            status: 200,
            data: Buffer.alloc(1024) // 模拟1KB的片段数据
          });
        }
        return Promise.reject(new Error('Unknown URL'));
      });

      // 开始下载（会被中断）
      const downloadPromise = downloader.downloadVideo(mockResource);

      // 等待一段时间后暂停下载
      setTimeout(() => {
        const taskId = (downloader as any).generateTaskId(mockResource);
        downloader.pauseDownload(taskId);
      }, 100);

      try {
        await downloadPromise;
      } catch (error) {
        // 预期会因为暂停而失败
      }

      // 检查是否保存了断点续传状态
      const taskId = (downloader as any).generateTaskId(mockResource);
      const canResume = await downloader.canResumeTask(taskId);
      expect(canResume).toBe(true);
    });

    it('应该能够从断点续传状态恢复下载', async () => {
      // 首先创建一个部分下载的状态
      const taskId = (downloader as any).generateTaskId(mockResource);
      const tempDir = path.join(testTempDir, taskId);
      await fs.ensureDir(tempDir);

      // 创建部分下载的片段文件
      const segment0Path = path.join(tempDir, 'segment_000000.ts');
      const segment1Path = path.join(tempDir, 'segment_000001.ts');
      await fs.writeFile(segment0Path, Buffer.alloc(1024));
      await fs.writeFile(segment1Path, Buffer.alloc(1024));

      // 手动创建断点续传状态
      const resumeStateManager = (downloader as any).resumeStateManager;
      await resumeStateManager.saveState({
        taskId,
        resource: mockResource,
        outputPath: fileOrganizer.generatePath(mockResource),
        tempDir,
        totalSegments: 5,
        downloadedSegments: [
          {
            index: 0,
            url: 'https://example.com/segment0.ts',
            filePath: segment0Path,
            size: 1024,
            downloadedAt: new Date(),
            isComplete: true
          },
          {
            index: 1,
            url: 'https://example.com/segment1.ts',
            filePath: segment1Path,
            size: 1024,
            downloadedAt: new Date(),
            isComplete: true
          }
        ],
        failedSegments: [],
        lastUpdateTime: new Date(),
        playlistUrl: mockResource.url,
        baseUrl: mockPlaylist.baseUrl
      });

      // Mock剩余片段的下载
      mockedAxios.get.mockImplementation((url) => {
        if (url.includes('segment2') || url.includes('segment3') || url.includes('segment4')) {
          return Promise.resolve({
            status: 200,
            data: Buffer.alloc(1024)
          });
        }
        return Promise.reject(new Error('Unknown URL'));
      });

      // 恢复下载
      const outputPath = await downloader.resumeDownload(mockResource);

      expect(outputPath).toBeDefined();
      expect(await fs.pathExists(outputPath)).toBe(true);
    });

    it('应该正确计算恢复下载的进度', async () => {
      const progressUpdates: number[] = [];
      
      // 创建部分下载状态（2/5片段已完成）
      const taskId = (downloader as any).generateTaskId(mockResource);
      const tempDir = path.join(testTempDir, taskId);
      await fs.ensureDir(tempDir);

      // 创建已下载的片段
      await fs.writeFile(path.join(tempDir, 'segment_000000.ts'), Buffer.alloc(1024));
      await fs.writeFile(path.join(tempDir, 'segment_000001.ts'), Buffer.alloc(1024));

      const resumeStateManager = (downloader as any).resumeStateManager;
      await resumeStateManager.saveState({
        taskId,
        resource: mockResource,
        outputPath: fileOrganizer.generatePath(mockResource),
        tempDir,
        totalSegments: 5,
        downloadedSegments: [
          {
            index: 0,
            url: 'https://example.com/segment0.ts',
            filePath: path.join(tempDir, 'segment_000000.ts'),
            size: 1024,
            downloadedAt: new Date(),
            isComplete: true
          },
          {
            index: 1,
            url: 'https://example.com/segment1.ts',
            filePath: path.join(tempDir, 'segment_000001.ts'),
            size: 1024,
            downloadedAt: new Date(),
            isComplete: true
          }
        ],
        failedSegments: [],
        lastUpdateTime: new Date(),
        playlistUrl: mockResource.url,
        baseUrl: mockPlaylist.baseUrl
      });

      // Mock剩余片段下载
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: Buffer.alloc(1024)
      });

      // 恢复下载并监控进度
      await downloader.resumeDownload(mockResource, (progress) => {
        progressUpdates.push(progress.progress);
      });

      // 验证进度从40%开始（2/5 * 80% = 32%，但会有一些调整）
      expect(progressUpdates[0]).toBeGreaterThan(30);
      expect(progressUpdates[progressUpdates.length - 1]).toBe(100);
    });
  });

  describe('网络中断处理', () => {
    it('应该在网络中断时暂停下载', async () => {
      // Mock网络监控器
      const mockNetworkMonitor = {
        startMonitoring: jest.fn(),
        stopMonitoring: jest.fn(),
        getCurrentStatus: jest.fn().mockReturnValue({
          isOnline: true,
          connectionType: 'wifi',
          effectiveType: '4g'
        }),
        getRecommendedDownloadStrategy: jest.fn().mockReturnValue({
          maxConcurrent: 3,
          chunkSize: 1024 * 1024,
          retryDelay: 1000
        }),
        on: jest.fn(),
        emit: jest.fn()
      };

      (downloader as any).networkMonitor = mockNetworkMonitor;
      (downloader as any).config.networkMonitoring = true;

      // 模拟网络中断
      setTimeout(() => {
        const offlineHandler = mockNetworkMonitor.on.mock.calls
          .find(call => call[0] === 'network-offline')?.[1];
        if (offlineHandler) {
          offlineHandler();
        }
      }, 100);

      // Mock axios请求（会被中断）
      mockedAxios.get.mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => resolve({
            status: 200,
            data: Buffer.alloc(1024)
          }), 200);
        });
      });

      try {
        await downloader.downloadVideo(mockResource);
      } catch (error) {
        // 预期会因为网络中断而失败
      }

      // 验证任务被暂停
      const taskId = (downloader as any).generateTaskId(mockResource);
      expect((downloader as any).pausedTasks.has(taskId)).toBe(true);
    });

    it('应该在网络恢复时自动恢复下载', async () => {
      const autoResumeSpy = jest.fn();
      
      // Mock网络监控器
      const mockNetworkMonitor = {
        startMonitoring: jest.fn(),
        stopMonitoring: jest.fn(),
        getCurrentStatus: jest.fn().mockReturnValue({
          isOnline: true
        }),
        on: jest.fn(),
        emit: jest.fn()
      };

      (downloader as any).networkMonitor = mockNetworkMonitor;
      (downloader as any).config.autoResumeOnReconnect = true;

      // 监听自动恢复事件
      downloader.on('auto-resume-success', autoResumeSpy);

      // 创建一个暂停的任务状态
      const taskId = (downloader as any).generateTaskId(mockResource);
      (downloader as any).pausedTasks.add(taskId);

      const tempDir = path.join(testTempDir, taskId);
      await fs.ensureDir(tempDir);
      await fs.writeFile(path.join(tempDir, 'segment_000000.ts'), Buffer.alloc(1024));

      const resumeStateManager = (downloader as any).resumeStateManager;
      await resumeStateManager.saveState({
        taskId,
        resource: mockResource,
        outputPath: fileOrganizer.generatePath(mockResource),
        tempDir,
        totalSegments: 5,
        downloadedSegments: [{
          index: 0,
          url: 'https://example.com/segment0.ts',
          filePath: path.join(tempDir, 'segment_000000.ts'),
          size: 1024,
          downloadedAt: new Date(),
          isComplete: true
        }],
        failedSegments: [],
        lastUpdateTime: new Date(),
        playlistUrl: mockResource.url,
        baseUrl: mockPlaylist.baseUrl
      });

      // Mock剩余片段下载
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: Buffer.alloc(1024)
      });

      // 模拟网络重连
      const reconnectHandler = mockNetworkMonitor.on.mock.calls
        .find(call => call[0] === 'network-reconnected')?.[1];
      
      if (reconnectHandler) {
        await reconnectHandler();
      }

      // 等待自动恢复完成
      await new Promise(resolve => setTimeout(resolve, 500));

      expect(autoResumeSpy).toHaveBeenCalledWith(taskId);
    });
  });

  describe('片段验证', () => {
    it('应该验证已下载片段的完整性', async () => {
      const taskId = (downloader as any).generateTaskId(mockResource);
      const tempDir = path.join(testTempDir, taskId);
      await fs.ensureDir(tempDir);

      // 创建一个损坏的片段文件（大小不匹配）
      const corruptedSegmentPath = path.join(tempDir, 'segment_000000.ts');
      await fs.writeFile(corruptedSegmentPath, Buffer.alloc(512)); // 实际大小与记录不符

      const resumeStateManager = (downloader as any).resumeStateManager;
      await resumeStateManager.saveState({
        taskId,
        resource: mockResource,
        outputPath: fileOrganizer.generatePath(mockResource),
        tempDir,
        totalSegments: 3,
        downloadedSegments: [{
          index: 0,
          url: 'https://example.com/segment0.ts',
          filePath: corruptedSegmentPath,
          size: 1024, // 记录的大小与实际不符
          downloadedAt: new Date(),
          isComplete: true
        }],
        failedSegments: [],
        lastUpdateTime: new Date(),
        playlistUrl: mockResource.url,
        baseUrl: mockPlaylist.baseUrl
      });

      // Mock所有片段的重新下载
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: Buffer.alloc(1024)
      });

      // 恢复下载，应该重新下载损坏的片段
      await downloader.resumeDownload(mockResource);

      // 验证损坏的片段被标记为失败并重新下载
      const updatedState = await resumeStateManager.loadState(taskId);
      expect(updatedState?.failedSegments).toContain(0);
    });
  });

  describe('错误处理', () => {
    it('应该在无法恢复时重新开始下载', async () => {
      const taskId = (downloader as any).generateTaskId(mockResource);
      
      // 创建一个无效的断点续传状态（临时目录不存在）
      const resumeStateManager = (downloader as any).resumeStateManager;
      await resumeStateManager.saveState({
        taskId,
        resource: mockResource,
        outputPath: fileOrganizer.generatePath(mockResource),
        tempDir: '/non-existent-dir',
        totalSegments: 3,
        downloadedSegments: [],
        failedSegments: [],
        lastUpdateTime: new Date(),
        playlistUrl: mockResource.url,
        baseUrl: mockPlaylist.baseUrl
      });

      // Mock完整下载
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: Buffer.alloc(1024)
      });

      // 尝试恢复下载，应该回退到完整下载
      const outputPath = await downloader.resumeDownload(mockResource);

      expect(outputPath).toBeDefined();
      expect(await fs.pathExists(outputPath)).toBe(true);
    });

    it('应该正确处理恢复过程中的错误', async () => {
      const taskId = (downloader as any).generateTaskId(mockResource);
      const tempDir = path.join(testTempDir, taskId);
      await fs.ensureDir(tempDir);

      const resumeStateManager = (downloader as any).resumeStateManager;
      await resumeStateManager.saveState({
        taskId,
        resource: mockResource,
        outputPath: fileOrganizer.generatePath(mockResource),
        tempDir,
        totalSegments: 3,
        downloadedSegments: [],
        failedSegments: [],
        lastUpdateTime: new Date(),
        playlistUrl: mockResource.url,
        baseUrl: mockPlaylist.baseUrl
      });

      // Mock下载失败
      mockedAxios.get.mockRejectedValue(new Error('Download failed'));

      // 尝试恢复下载，应该抛出错误
      await expect(downloader.resumeDownload(mockResource)).rejects.toThrow();
    });
  });
});
