import { NetworkMonitor } from '../NetworkMonitor';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock navigator
const mockNavigator = {
  onLine: true,
  connection: {
    type: 'wifi',
    effectiveType: '4g',
    downlink: 10,
    rtt: 50,
    saveData: false,
    addEventListener: jest.fn()
  }
};

Object.defineProperty(global, 'navigator', {
  value: mockNavigator,
  writable: true
});

// Mock window events
const mockWindow = {
  addEventListener: jest.fn()
};

Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
});

describe('NetworkMonitor', () => {
  let networkMonitor: NetworkMonitor;

  beforeEach(() => {
    jest.clearAllMocks();
    networkMonitor = new NetworkMonitor({
      checkInterval: 1000, // 1秒用于测试
      timeoutDuration: 5000,
      testUrls: ['https://test1.com', 'https://test2.com'],
      retryAttempts: 2
    });
  });

  afterEach(() => {
    networkMonitor.stopMonitoring();
  });

  describe('初始化', () => {
    it('应该正确初始化网络监控器', () => {
      expect(networkMonitor).toBeDefined();
      expect(networkMonitor.getCurrentStatus().isOnline).toBe(true);
    });

    it('应该设置浏览器事件监听器', () => {
      expect(mockWindow.addEventListener).toHaveBeenCalledWith('online', expect.any(Function));
      expect(mockWindow.addEventListener).toHaveBeenCalledWith('offline', expect.any(Function));
    });
  });

  describe('网络状态检测', () => {
    it('应该检测到在线状态', async () => {
      // Mock successful axios requests
      mockedAxios.get.mockResolvedValue({ status: 200 });

      networkMonitor.startMonitoring();

      // 等待检测完成
      await new Promise(resolve => setTimeout(resolve, 100));

      const status = networkMonitor.getCurrentStatus();
      expect(status.isOnline).toBe(true);
      expect(status.rtt).toBeGreaterThan(0);
    });

    it('应该检测到离线状态', async () => {
      // Mock failed axios requests
      mockedAxios.get.mockRejectedValue(new Error('Network error'));

      networkMonitor.startMonitoring();

      // 等待检测完成
      await new Promise(resolve => setTimeout(resolve, 100));

      const status = networkMonitor.getCurrentStatus();
      expect(status.isOnline).toBe(false);
    });

    it('应该计算平均RTT', async () => {
      // Mock different response times
      mockedAxios.get
        .mockResolvedValueOnce({ status: 200 })
        .mockResolvedValueOnce({ status: 200 });

      networkMonitor.startMonitoring();

      // 等待检测完成
      await new Promise(resolve => setTimeout(resolve, 100));

      const status = networkMonitor.getCurrentStatus();
      expect(status.rtt).toBeGreaterThan(0);
    });
  });

  describe('网络质量评估', () => {
    it('应该在网络良好时返回高质量分数', () => {
      // 设置良好的网络状态
      const goodStatus = {
        isOnline: true,
        connectionType: 'wifi' as const,
        effectiveType: '4g' as const,
        downlink: 10,
        rtt: 50,
        saveData: false,
        lastChecked: new Date()
      };

      // 使用反射设置内部状态
      (networkMonitor as any).currentStatus = goodStatus;
      (networkMonitor as any).consecutiveFailures = 0;

      const quality = networkMonitor.getNetworkQuality();
      expect(quality).toBeGreaterThan(80);
    });

    it('应该在网络较差时返回低质量分数', () => {
      // 设置较差的网络状态
      const poorStatus = {
        isOnline: true,
        connectionType: 'cellular' as const,
        effectiveType: '2g' as const,
        downlink: 0.5,
        rtt: 2000,
        saveData: true,
        lastChecked: new Date()
      };

      (networkMonitor as any).currentStatus = poorStatus;
      (networkMonitor as any).consecutiveFailures = 2;

      const quality = networkMonitor.getNetworkQuality();
      expect(quality).toBeLessThan(50);
    });

    it('应该在离线时返回0分数', () => {
      const offlineStatus = {
        isOnline: false,
        connectionType: 'unknown' as const,
        effectiveType: 'unknown' as const,
        downlink: 0,
        rtt: 0,
        saveData: false,
        lastChecked: new Date()
      };

      (networkMonitor as any).currentStatus = offlineStatus;

      const quality = networkMonitor.getNetworkQuality();
      expect(quality).toBe(0);
    });
  });

  describe('下载策略推荐', () => {
    it('应该为高质量网络推荐激进策略', () => {
      (networkMonitor as any).currentStatus = {
        isOnline: true,
        rtt: 50,
        effectiveType: '4g'
      };
      (networkMonitor as any).consecutiveFailures = 0;

      const strategy = networkMonitor.getRecommendedDownloadStrategy();
      expect(strategy.maxConcurrent).toBeGreaterThan(4);
      expect(strategy.chunkSize).toBeGreaterThan(512 * 1024);
      expect(strategy.retryDelay).toBeLessThan(2000);
    });

    it('应该为低质量网络推荐保守策略', () => {
      (networkMonitor as any).currentStatus = {
        isOnline: true,
        rtt: 2000,
        effectiveType: '2g'
      };
      (networkMonitor as any).consecutiveFailures = 3;

      const strategy = networkMonitor.getRecommendedDownloadStrategy();
      expect(strategy.maxConcurrent).toBeLessThanOrEqual(2);
      expect(strategy.chunkSize).toBeLessThan(256 * 1024);
      expect(strategy.retryDelay).toBeGreaterThan(3000);
    });
  });

  describe('事件发射', () => {
    it('应该在网络状态变化时发射事件', async () => {
      const onlineSpy = jest.fn();
      const offlineSpy = jest.fn();

      networkMonitor.on('network-online', onlineSpy);
      networkMonitor.on('network-offline', offlineSpy);

      // 模拟从离线到在线
      (networkMonitor as any).currentStatus.isOnline = false;
      mockedAxios.get.mockResolvedValue({ status: 200 });

      networkMonitor.startMonitoring();

      // 等待状态检测
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(onlineSpy).toHaveBeenCalled();
    });

    it('应该在网络重连时发射重连事件', async () => {
      const reconnectedSpy = jest.fn();
      networkMonitor.on('network-reconnected', reconnectedSpy);

      // 设置初始离线状态
      (networkMonitor as any).currentStatus.isOnline = false;
      
      // 模拟网络恢复
      mockedAxios.get.mockResolvedValue({ status: 200 });

      networkMonitor.startMonitoring();

      // 等待状态检测
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(reconnectedSpy).toHaveBeenCalled();
    });

    it('应该在网络速度变化时发射事件', async () => {
      const speedChangedSpy = jest.fn();
      networkMonitor.on('network-speed-changed', speedChangedSpy);

      // 设置初始状态
      (networkMonitor as any).currentStatus = {
        isOnline: true,
        rtt: 100
      };

      // 模拟速度变化
      mockedAxios.get.mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => resolve({ status: 200 }), 500); // 模拟慢速连接
        });
      });

      networkMonitor.startMonitoring();

      // 等待状态检测
      await new Promise(resolve => setTimeout(resolve, 600));

      // 由于RTT变化，应该触发速度变化事件
      expect(speedChangedSpy).toHaveBeenCalled();
    });
  });

  describe('监控控制', () => {
    it('应该能够启动和停止监控', () => {
      expect(networkMonitor.isNetworkStable()).toBe(true);

      networkMonitor.startMonitoring();
      expect((networkMonitor as any).isMonitoring).toBe(true);

      networkMonitor.stopMonitoring();
      expect((networkMonitor as any).isMonitoring).toBe(false);
    });

    it('应该在重复启动时不创建多个定时器', () => {
      networkMonitor.startMonitoring();
      const firstTimer = (networkMonitor as any).checkTimer;

      networkMonitor.startMonitoring();
      const secondTimer = (networkMonitor as any).checkTimer;

      expect(firstTimer).toBe(secondTimer);
    });
  });

  describe('网络稳定性检查', () => {
    it('应该在网络稳定时返回true', () => {
      (networkMonitor as any).currentStatus.isOnline = true;
      (networkMonitor as any).consecutiveFailures = 0;

      expect(networkMonitor.isNetworkStable()).toBe(true);
    });

    it('应该在网络不稳定时返回false', () => {
      (networkMonitor as any).currentStatus.isOnline = false;
      (networkMonitor as any).consecutiveFailures = 3;

      expect(networkMonitor.isNetworkStable()).toBe(false);
    });

    it('应该在有连续失败时返回false', () => {
      (networkMonitor as any).currentStatus.isOnline = true;
      (networkMonitor as any).consecutiveFailures = 2;

      expect(networkMonitor.isNetworkStable()).toBe(false);
    });
  });

  describe('连接类型检测', () => {
    it('应该正确检测WiFi连接', () => {
      mockNavigator.connection.type = 'wifi';
      
      const connectionType = (networkMonitor as any).getConnectionType();
      expect(connectionType).toBe('wifi');
    });

    it('应该正确检测蜂窝网络连接', () => {
      mockNavigator.connection.type = 'cellular';
      
      const connectionType = (networkMonitor as any).getConnectionType();
      expect(connectionType).toBe('cellular');
    });

    it('应该在不支持连接API时返回unknown', () => {
      const originalConnection = mockNavigator.connection;
      delete (mockNavigator as any).connection;
      
      const connectionType = (networkMonitor as any).getConnectionType();
      expect(connectionType).toBe('unknown');

      // 恢复
      mockNavigator.connection = originalConnection;
    });
  });

  describe('有效连接类型检测', () => {
    it('应该基于RTT正确分类连接类型', () => {
      // 测试不同RTT值的分类
      // RTT逻辑: >2000=slow-2g, >1400=2g, >270=3g, <=270=4g
      expect((networkMonitor as any).getEffectiveType(50)).toBe('4g');   // 50 <= 270
      expect((networkMonitor as any).getEffectiveType(300)).toBe('3g');  // 300 > 270
      expect((networkMonitor as any).getEffectiveType(1500)).toBe('2g'); // 1500 > 1400
      expect((networkMonitor as any).getEffectiveType(2500)).toBe('slow-2g'); // 2500 > 2000
    });

    it('应该优先使用浏览器提供的有效连接类型', () => {
      mockNavigator.connection.effectiveType = '3g';
      
      const effectiveType = (networkMonitor as any).getEffectiveType(50);
      expect(effectiveType).toBe('3g');
    });
  });
});
