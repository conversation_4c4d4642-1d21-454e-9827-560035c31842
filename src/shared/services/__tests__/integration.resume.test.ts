import * as fs from 'fs-extra';
import * as path from 'path';
import { DownloadManager } from '../DownloadManager';
import { M3U8Downloader } from '../M3U8Downloader';
import { FileOrganizer } from '../FileOrganizer';
import { ResumeStateManager } from '../ResumeStateManager';
import { NetworkMonitor } from '../NetworkMonitor';
import { CourseResource } from '../../types';

// Mock external dependencies
jest.mock('axios');
jest.mock('../M3U8Parser');
jest.mock('../VideoMerger');

describe('断点续传功能集成测试', () => {
  let downloadManager: DownloadManager;
  let fileOrganizer: FileOrganizer;
  let testDir: string;
  let mockResource: CourseResource;

  beforeEach(async () => {
    // 创建测试目录
    testDir = path.join(__dirname, 'integration-test');
    await fs.ensureDir(testDir);

    // 创建文件组织器
    fileOrganizer = new FileOrganizer({
      basePath: testDir,
      namingPattern: '{title}',
      createSubfolders: false
    });

    // 创建下载管理器
    downloadManager = new DownloadManager(fileOrganizer, {
      maxConcurrentDownloads: 2,
      autoRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      enableNotifications: false
    });

    // 模拟视频资源
    mockResource = {
      id: 'integration-test-video',
      title: '集成测试视频',
      type: 'video',
      url: 'https://example.com/test.m3u8',
      metadata: {
        stage: '小学',
        grade: '三年级',
        subject: '数学',
        version: '人教版',
        volume: '上册'
      },
      requiresAuth: false
    };
  });

  afterEach(async () => {
    // 清理测试目录
    await fs.remove(testDir);
  });

  describe('完整的断点续传流程', () => {
    it('应该能够完成暂停-恢复-完成的完整流程', async () => {
      const events: string[] = [];
      
      // 监听所有相关事件
      downloadManager.on('task-added', () => events.push('task-added'));
      downloadManager.on('task-started', () => events.push('task-started'));
      downloadManager.on('task-progress', () => events.push('task-progress'));
      downloadManager.on('task-paused', () => events.push('task-paused'));
      downloadManager.on('task-resuming', () => events.push('task-resuming'));
      downloadManager.on('task-completed', () => events.push('task-completed'));

      // 添加下载任务
      const task = downloadManager.addTask(mockResource);
      expect(events).toContain('task-added');

      // 等待任务开始
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(events).toContain('task-started');

      // 暂停任务
      await downloadManager.pauseTask(task.id);
      expect(events).toContain('task-paused');
      expect(task.status).toBe('paused');

      // 恢复任务
      await downloadManager.resumeTask(task.id);
      
      // 等待任务完成
      await new Promise(resolve => {
        downloadManager.on('task-completed', resolve);
      });

      expect(events).toContain('task-completed');
      expect(task.status).toBe('completed');
    }, 10000);

    it('应该能够处理多次暂停和恢复', async () => {
      const task = downloadManager.addTask(mockResource);
      
      // 第一次暂停和恢复
      await new Promise(resolve => setTimeout(resolve, 50));
      await downloadManager.pauseTask(task.id);
      expect(task.status).toBe('paused');
      
      await downloadManager.resumeTask(task.id);
      
      // 第二次暂停和恢复
      await new Promise(resolve => setTimeout(resolve, 50));
      await downloadManager.pauseTask(task.id);
      expect(task.status).toBe('paused');
      
      await downloadManager.resumeTask(task.id);
      
      // 验证恢复次数
      expect(task.resumeState?.resumeCount).toBeGreaterThan(0);
    }, 10000);
  });

  describe('网络中断模拟', () => {
    it('应该能够处理网络中断和恢复', async () => {
      // 创建带网络监控的下载管理器
      const networkMonitor = new NetworkMonitor({
        checkInterval: 100,
        enableDetailedInfo: true
      });

      const m3u8Downloader = new M3U8Downloader(fileOrganizer, {
        enableResume: true,
        networkMonitoring: true,
        autoResumeOnReconnect: true
      });

      // 替换下载管理器中的M3U8下载器
      (downloadManager as any).m3u8Downloader = m3u8Downloader;

      const task = downloadManager.addTask(mockResource);
      
      // 等待任务开始
      await new Promise(resolve => setTimeout(resolve, 100));

      // 模拟网络中断
      networkMonitor.emit('network-offline', {
        type: 'offline',
        status: {
          isOnline: false,
          connectionType: 'unknown',
          effectiveType: 'unknown',
          downlink: 0,
          rtt: 0,
          saveData: false,
          lastChecked: new Date()
        },
        timestamp: new Date()
      });

      // 验证任务被暂停
      expect(task.status).toBe('paused');

      // 模拟网络恢复
      networkMonitor.emit('network-reconnected', {
        type: 'reconnected',
        status: {
          isOnline: true,
          connectionType: 'wifi',
          effectiveType: '4g',
          downlink: 10,
          rtt: 50,
          saveData: false,
          lastChecked: new Date()
        },
        timestamp: new Date()
      });

      // 等待自动恢复
      await new Promise(resolve => setTimeout(resolve, 200));

      // 验证任务自动恢复
      expect(task.status).toBe('downloading');
    }, 10000);
  });

  describe('状态持久化', () => {
    it('应该能够在应用重启后恢复下载状态', async () => {
      const stateDir = path.join(testDir, 'states');
      const resumeStateManager = new ResumeStateManager({
        stateDir,
        enableChecksum: true,
        autoCleanup: false
      });

      // 创建一个部分下载的状态
      const taskId = 'test-task-persistence';
      const tempDir = path.join(testDir, 'temp');
      await fs.ensureDir(tempDir);

      // 创建模拟的片段文件
      const segment0Path = path.join(tempDir, 'segment_000000.ts');
      const segment1Path = path.join(tempDir, 'segment_000001.ts');
      await fs.writeFile(segment0Path, Buffer.alloc(1024));
      await fs.writeFile(segment1Path, Buffer.alloc(1024));

      // 保存断点续传状态
      await resumeStateManager.saveState({
        taskId,
        resource: mockResource,
        outputPath: path.join(testDir, 'output.mp4'),
        tempDir,
        totalSegments: 5,
        downloadedSegments: [
          {
            index: 0,
            url: 'https://example.com/segment0.ts',
            filePath: segment0Path,
            size: 1024,
            downloadedAt: new Date(),
            isComplete: true
          },
          {
            index: 1,
            url: 'https://example.com/segment1.ts',
            filePath: segment1Path,
            size: 1024,
            downloadedAt: new Date(),
            isComplete: true
          }
        ],
        failedSegments: [],
        lastUpdateTime: new Date(),
        playlistUrl: mockResource.url,
        baseUrl: 'https://example.com/'
      });

      // 验证状态文件存在
      const stateFilePath = path.join(stateDir, `${taskId}.json`);
      expect(await fs.pathExists(stateFilePath)).toBe(true);

      // 创建新的状态管理器实例（模拟应用重启）
      const newResumeStateManager = new ResumeStateManager({
        stateDir,
        enableChecksum: true,
        autoCleanup: false
      });

      // 加载状态
      const loadedState = await newResumeStateManager.loadState(taskId);
      expect(loadedState).not.toBeNull();
      expect(loadedState!.downloadedSegments).toHaveLength(2);
      expect(loadedState!.totalSegments).toBe(5);

      // 验证可以续传
      const canResume = await newResumeStateManager.canResume(taskId);
      expect(canResume).toBe(true);
    });

    it('应该能够清理过期的状态文件', async () => {
      const stateDir = path.join(testDir, 'states-cleanup');
      const resumeStateManager = new ResumeStateManager({
        stateDir,
        maxStateAge: 100, // 100ms 过期时间
        autoCleanup: true
      });

      // 创建一个状态
      await resumeStateManager.saveState({
        taskId: 'expired-task',
        resource: mockResource,
        outputPath: '/test/output.mp4',
        tempDir: '/test/temp',
        totalSegments: 5,
        downloadedSegments: [],
        failedSegments: [],
        lastUpdateTime: new Date(),
        playlistUrl: mockResource.url,
        baseUrl: 'https://example.com/'
      });

      const stateFilePath = path.join(stateDir, 'expired-task.json');
      expect(await fs.pathExists(stateFilePath)).toBe(true);

      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 150));

      // 手动触发清理
      await resumeStateManager.cleanupExpiredStates();

      // 验证文件被清理
      expect(await fs.pathExists(stateFilePath)).toBe(false);
    });
  });

  describe('错误处理和恢复', () => {
    it('应该能够处理片段下载失败并重试', async () => {
      const task = downloadManager.addTask(mockResource);
      
      // 模拟部分片段下载失败
      const m3u8Downloader = (downloadManager as any).m3u8Downloader;
      const originalDownloadSegment = m3u8Downloader.downloadSegment;
      
      let failCount = 0;
      m3u8Downloader.downloadSegment = jest.fn().mockImplementation(async (segment, outputPath) => {
        if (failCount < 2 && segment.url.includes('segment2')) {
          failCount++;
          throw new Error('模拟下载失败');
        }
        return originalDownloadSegment.call(m3u8Downloader, segment, outputPath);
      });

      // 等待任务完成或失败
      await new Promise(resolve => {
        downloadManager.on('task-completed', resolve);
        downloadManager.on('task-failed', resolve);
      });

      // 验证任务最终完成（经过重试）
      expect(task.status).toBe('completed');
    }, 15000);

    it('应该能够处理状态文件损坏的情况', async () => {
      const stateDir = path.join(testDir, 'corrupted-states');
      await fs.ensureDir(stateDir);

      // 创建一个损坏的状态文件
      const corruptedStateFile = path.join(stateDir, 'corrupted-task.json');
      await fs.writeFile(corruptedStateFile, '{ invalid json }');

      const resumeStateManager = new ResumeStateManager({
        stateDir,
        enableChecksum: true
      });

      // 尝试加载损坏的状态
      const loadedState = await resumeStateManager.loadState('corrupted-task');
      expect(loadedState).toBeNull();

      // 验证损坏的文件被清理
      expect(await fs.pathExists(corruptedStateFile)).toBe(false);
    });
  });

  describe('性能和并发', () => {
    it('应该能够处理多个并发的断点续传任务', async () => {
      const tasks = [];
      const taskCount = 5;

      // 创建多个任务
      for (let i = 0; i < taskCount; i++) {
        const resource = {
          ...mockResource,
          id: `concurrent-task-${i}`,
          title: `并发测试任务 ${i}`
        };
        tasks.push(downloadManager.addTask(resource));
      }

      // 等待所有任务开始
      await new Promise(resolve => setTimeout(resolve, 200));

      // 暂停所有任务
      await downloadManager.pauseAll();
      
      // 验证所有任务都被暂停
      tasks.forEach(task => {
        expect(task.status).toBe('paused');
      });

      // 恢复所有任务
      downloadManager.resumeAll();

      // 等待所有任务完成
      await Promise.all(tasks.map(task => 
        new Promise(resolve => {
          const checkStatus = () => {
            if (task.status === 'completed' || task.status === 'failed') {
              resolve(task);
            } else {
              setTimeout(checkStatus, 100);
            }
          };
          checkStatus();
        })
      ));

      // 验证所有任务都完成
      tasks.forEach(task => {
        expect(['completed', 'failed']).toContain(task.status);
      });
    }, 20000);
  });
});
