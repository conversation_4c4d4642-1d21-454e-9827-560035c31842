import { SmartEduClient } from './SmartEduClient';
import { 
  CourseFilters, 
  FilterOption, 
  FilterOptions,
  CacheEntry,
  CacheOptions,
  ApiError
} from '../types';

/**
 * 筛选条件管理服务
 * 提供筛选条件的获取、缓存和联动逻辑
 */
export class FilterService {
  private client: SmartEduClient;
  private cache: Map<string, CacheEntry<FilterOption[]>>;
  private cacheOptions: CacheOptions;

  constructor(client: SmartEduClient, cacheOptions?: Partial<CacheOptions>) {
    this.client = client;
    this.cache = new Map();
    this.cacheOptions = {
      ttl: 5 * 60 * 1000, // 5分钟缓存
      maxSize: 100,
      cleanupInterval: 60 * 1000, // 1分钟清理一次
      ...cacheOptions
    };

    // 启动缓存清理定时器
    this.startCacheCleanup();
  }

  /**
   * 启动缓存清理定时器
   */
  private startCacheCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredCache();
    }, this.cacheOptions.cleanupInterval);
  }

  /**
   * 清理过期缓存
   */
  private cleanupExpiredCache(): void {
    const now = new Date();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt < now) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));

    // 如果缓存大小超过限制，删除最旧的条目
    if (this.cache.size > this.cacheOptions.maxSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp.getTime() - b[1].timestamp.getTime());
      
      const toDelete = entries.slice(0, this.cache.size - this.cacheOptions.maxSize);
      toDelete.forEach(([key]) => this.cache.delete(key));
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, params: Record<string, string> = {}): string {
    const paramStr = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    return `${type}:${paramStr}`;
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache(key: string): FilterOption[] | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = new Date();
    if (entry.expiresAt < now) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * 存储到缓存
   */
  private setToCache(key: string, data: FilterOption[]): void {
    const now = new Date();
    const entry: CacheEntry<FilterOption[]> = {
      key,
      data,
      timestamp: now,
      expiresAt: new Date(now.getTime() + this.cacheOptions.ttl)
    };

    this.cache.set(key, entry);
  }

  /**
   * 获取学段列表
   */
  async getStages(): Promise<FilterOption[]> {
    const cacheKey = this.generateCacheKey('stages');
    
    // 尝试从缓存获取
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const stages = await this.client.getStages();
      this.setToCache(cacheKey, stages);
      return stages;
    } catch (error) {
      throw new ApiError('获取学段列表失败', { originalError: error as Error });
    }
  }

  /**
   * 根据学段获取年级列表
   */
  async getGradesByStage(stage: string): Promise<FilterOption[]> {
    if (!stage) {
      return [];
    }

    const cacheKey = this.generateCacheKey('grades', { stage });
    
    // 尝试从缓存获取
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const grades = await this.client.getGradesByStage(stage);
      this.setToCache(cacheKey, grades);
      return grades;
    } catch (error) {
      throw new ApiError('获取年级列表失败', { 
        originalError: error as Error,
        context: { stage }
      });
    }
  }

  /**
   * 根据学段和年级获取学科列表
   */
  async getSubjectsByStageAndGrade(stage: string, grade: string): Promise<FilterOption[]> {
    if (!stage || !grade) {
      return [];
    }

    const cacheKey = this.generateCacheKey('subjects', { stage, grade });
    
    // 尝试从缓存获取
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const subjects = await this.client.getSubjectsByStageAndGrade(stage, grade);
      this.setToCache(cacheKey, subjects);
      return subjects;
    } catch (error) {
      throw new ApiError('获取学科列表失败', { 
        originalError: error as Error,
        context: { stage, grade }
      });
    }
  }

  /**
   * 根据学段、年级和学科获取版本列表
   */
  async getVersionsByStageGradeSubject(
    stage: string, 
    grade: string, 
    subject: string
  ): Promise<FilterOption[]> {
    if (!stage || !grade || !subject) {
      return [];
    }

    const cacheKey = this.generateCacheKey('versions', { stage, grade, subject });
    
    // 尝试从缓存获取
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const versions = await this.client.getVersionsByStageGradeSubject(stage, grade, subject);
      this.setToCache(cacheKey, versions);
      return versions;
    } catch (error) {
      throw new ApiError('获取版本列表失败', { 
        originalError: error as Error,
        context: { stage, grade, subject }
      });
    }
  }

  /**
   * 根据学段、年级、学科和版本获取册次列表
   */
  async getVolumesByStageGradeSubjectVersion(
    stage: string, 
    grade: string, 
    subject: string, 
    version: string
  ): Promise<FilterOption[]> {
    if (!stage || !grade || !subject || !version) {
      return [];
    }

    const cacheKey = this.generateCacheKey('volumes', { stage, grade, subject, version });
    
    // 尝试从缓存获取
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const volumes = await this.client.getVolumesByStageGradeSubjectVersion(
        stage, grade, subject, version
      );
      this.setToCache(cacheKey, volumes);
      return volumes;
    } catch (error) {
      throw new ApiError('获取册次列表失败', { 
        originalError: error as Error,
        context: { stage, grade, subject, version }
      });
    }
  }

  /**
   * 获取完整的筛选选项（级联获取）
   */
  async getFilterOptions(parentFilter?: Partial<CourseFilters>): Promise<FilterOptions> {
    try {
      const options: FilterOptions = {
        stages: [],
        grades: [],
        subjects: [],
        versions: [],
        volumes: []
      };

      // 获取学段列表
      options.stages = await this.getStages();

      // 如果指定了学段，获取年级列表
      if (parentFilter?.stage) {
        options.grades = await this.getGradesByStage(parentFilter.stage);

        // 如果指定了年级，获取学科列表
        if (parentFilter.grade) {
          options.subjects = await this.getSubjectsByStageAndGrade(
            parentFilter.stage, 
            parentFilter.grade
          );

          // 如果指定了学科，获取版本列表
          if (parentFilter.subject) {
            options.versions = await this.getVersionsByStageGradeSubject(
              parentFilter.stage,
              parentFilter.grade,
              parentFilter.subject
            );

            // 如果指定了版本，获取册次列表
            if (parentFilter.version) {
              options.volumes = await this.getVolumesByStageGradeSubjectVersion(
                parentFilter.stage,
                parentFilter.grade,
                parentFilter.subject,
                parentFilter.version
              );
            }
          }
        }
      }

      return options;
    } catch (error) {
      throw new ApiError('获取筛选选项失败', { 
        originalError: error as Error,
        context: { parentFilter }
      });
    }
  }

  /**
   * 验证筛选条件的完整性
   */
  validateFilters(filters: Partial<CourseFilters>): {
    isValid: boolean;
    missingFields: string[];
    errors: string[];
  } {
    const missingFields: string[] = [];
    const errors: string[] = [];

    // 检查必填字段
    if (!filters.stage) missingFields.push('stage');
    if (!filters.grade) missingFields.push('grade');
    if (!filters.subject) missingFields.push('subject');
    if (!filters.version) missingFields.push('version');
    if (!filters.volume) missingFields.push('volume');

    // 检查字段格式
    if (filters.stage && typeof filters.stage !== 'string') {
      errors.push('学段必须是字符串类型');
    }
    if (filters.grade && typeof filters.grade !== 'string') {
      errors.push('年级必须是字符串类型');
    }
    if (filters.subject && typeof filters.subject !== 'string') {
      errors.push('学科必须是字符串类型');
    }
    if (filters.version && typeof filters.version !== 'string') {
      errors.push('版本必须是字符串类型');
    }
    if (filters.volume && typeof filters.volume !== 'string') {
      errors.push('册次必须是字符串类型');
    }

    return {
      isValid: missingFields.length === 0 && errors.length === 0,
      missingFields,
      errors
    };
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    entries: Array<{
      key: string;
      timestamp: Date;
      expiresAt: Date;
    }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      timestamp: entry.timestamp,
      expiresAt: entry.expiresAt
    }));

    return {
      size: this.cache.size,
      maxSize: this.cacheOptions.maxSize,
      hitRate: 0, // TODO: 实现命中率统计
      entries
    };
  }

  /**
   * 销毁服务，清理资源
   */
  destroy(): void {
    this.clearCache();
  }
}

export default FilterService;