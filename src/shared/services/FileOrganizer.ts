import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import { CourseResource, FileOrganizationConfig, FileInfo, FileError, ValidationError } from '../types';

/**
 * 文件注册表统计信息
 */
export interface RegistryStats {
  totalFiles: number;
  validFiles: number;
  invalidFiles: number;
  totalSize: number;
}

/**
 * 文件组织器
 * 负责按照配置规则组织和管理下载的教育资源文件
 */
export class FileOrganizer {
  private config: FileOrganizationConfig;
  private fileRegistry: Map<string, FileInfo> = new Map();

  constructor(config: FileOrganizationConfig) {
    this.config = config;
    this.validateConfig();
  }

  /**
   * 验证配置
   */
  private validateConfig(): void {
    if (!this.config.basePath) {
      throw new ValidationError('基础路径不能为空');
    }
    if (!this.config.namingPattern) {
      throw new ValidationError('命名模式不能为空');
    }
  }

  /**
   * 生成文件路径
   * @param resource 课程资源
   * @returns 完整的文件路径
   */
  public generatePath(resource: CourseResource): string {
    try {
      // 构建目录路径
      let dirPath = this.config.basePath;

      if (this.config.createSubfolders) {
        if (this.config.groupBySubject && resource.metadata?.subject) {
          dirPath = path.join(dirPath, this.sanitizeFileName(resource.metadata.subject));
        }
        if (this.config.groupByGrade && resource.metadata?.grade) {
          dirPath = path.join(dirPath, this.sanitizeFileName(resource.metadata.grade));
        }
      }

      // 生成文件名
      const fileName = this.generateFileName(resource);
      
      // 获取文件扩展名
      const extension = this.getFileExtension(resource.url);
      const fullFileName = extension ? `${fileName}.${extension}` : fileName;

      return path.join(dirPath, fullFileName);
    } catch (error) {
      throw new FileError(`生成文件路径失败: ${error.message}`, {
        originalError: error,
        context: { resource, config: this.config }
      });
    }
  }

  /**
   * 生成文件名
   * @param resource 课程资源
   * @returns 文件名（不含扩展名）
   */
  private generateFileName(resource: CourseResource): string {
    let fileName = this.config.namingPattern;

    // 替换占位符
    const replacements: Record<string, string> = {
      '{stage}': resource.metadata?.stage || '',
      '{grade}': resource.metadata?.grade || '',
      '{subject}': resource.metadata?.subject || '',
      '{version}': resource.metadata?.version || '',
      '{volume}': resource.metadata?.volume || '',
      '{chapter}': resource.metadata?.chapter || '',
      '{lesson}': resource.metadata?.lesson || '',
      '{title}': resource.title || '',
      '{type}': resource.type || '',
      '{id}': resource.id || ''
    };

    for (const [placeholder, value] of Object.entries(replacements)) {
      fileName = fileName.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), 
        this.sanitizeFileName(value));
    }

    // 清理连续的分隔符
    fileName = fileName.replace(/[-_\s]+/g, '-').replace(/^-+|-+$/g, '');

    return fileName || 'unnamed';
  }

  /**
   * 清理文件名中的非法字符
   * @param name 原始名称
   * @returns 清理后的名称
   */
  private sanitizeFileName(name: string): string {
    if (!name) return '';
    
    // 移除或替换非法字符
    return name
      .replace(/[<>:"/\\|?*]/g, '') // 移除Windows非法字符
      .replace(/[\x00-\x1f\x80-\x9f]/g, '') // 移除控制字符
      .replace(/^\.+/, '') // 移除开头的点
      .trim();
  }

  /**
   * 获取文件扩展名
   * @param url 文件URL
   * @returns 文件扩展名
   */
  private getFileExtension(url: string): string {
    try {
      const urlPath = new URL(url).pathname;
      const extension = path.extname(urlPath).slice(1).toLowerCase();
      return extension;
    } catch {
      return '';
    }
  }

  /**
   * 创建目录结构
   * @param filePath 文件路径
   */
  public async createDirectoryStructure(filePath: string): Promise<void> {
    try {
      const dirPath = path.dirname(filePath);
      await fs.ensureDir(dirPath);
    } catch (error) {
      throw new FileError(`创建目录结构失败: ${error.message}`, {
        originalError: error,
        context: { filePath }
      });
    }
  }

  /**
   * 检查重复文件
   * @param filePath 文件路径
   * @param resource 课程资源
   * @returns 是否为重复文件
   */
  public async checkDuplicateFile(filePath: string, resource: CourseResource): Promise<boolean> {
    try {
      // 检查文件是否存在
      if (!(await fs.pathExists(filePath))) {
        return false;
      }

      // 检查注册表中是否有相同资源
      const existingFile = this.fileRegistry.get(resource.id);
      if (existingFile && existingFile.path === filePath) {
        return true;
      }

      // 如果有文件大小信息，比较文件大小
      if (resource.metadata?.fileSize) {
        const stats = await fs.stat(filePath);
        if (stats.size === resource.metadata.fileSize) {
          return true;
        }
      }

      return false;
    } catch (error) {
      // 如果检查过程中出错，假设不是重复文件
      return false;
    }
  }

  /**
   * 检查版本更新
   * @param filePath 文件路径
   * @param resource 课程资源
   * @returns 是否需要更新
   */
  public async checkVersionUpdate(filePath: string, resource: CourseResource): Promise<boolean> {
    try {
      // 检查文件是否存在
      if (!(await fs.pathExists(filePath))) {
        return true; // 文件不存在，需要下载
      }

      const existingFile = this.fileRegistry.get(resource.id);
      if (!existingFile) {
        return true; // 没有注册信息，需要更新
      }

      // 比较文件大小
      if (resource.metadata?.fileSize && existingFile.size !== resource.metadata.fileSize) {
        return true;
      }

      // 检查文件完整性
      if (!existingFile.isValid) {
        return true;
      }

      return false;
    } catch (error) {
      // 如果检查过程中出错，假设需要更新
      return true;
    }
  }

  /**
   * 注册文件
   * @param resource 课程资源
   * @param filePath 文件路径
   */
  public async registerFile(resource: CourseResource, filePath: string): Promise<void> {
    try {
      // 检查文件是否存在
      if (!(await fs.pathExists(filePath))) {
        throw new FileError('文件不存在，无法注册');
      }

      const stats = await fs.stat(filePath);
      
      // 计算文件校验和
      const checksum = await this.calculateChecksum(filePath);

      const fileInfo: FileInfo = {
        path: filePath,
        size: stats.size,
        checksum,
        createdAt: new Date(),
        isValid: true
      };

      this.fileRegistry.set(resource.id, fileInfo);
    } catch (error) {
      throw new FileError(`注册文件失败: ${error.message}`, {
        originalError: error,
        context: { resource, filePath }
      });
    }
  }

  /**
   * 计算文件校验和
   * @param filePath 文件路径
   * @returns 文件校验和
   */
  private async calculateChecksum(filePath: string): Promise<string> {
    try {
      const hash = crypto.createHash('md5');
      const stream = fs.createReadStream(filePath);

      return new Promise((resolve, reject) => {
        stream.on('data', (data) => {
          hash.update(data);
        });

        stream.on('end', () => {
          resolve(hash.digest('hex'));
        });

        stream.on('error', (error) => {
          reject(error);
        });
      });
    } catch (error) {
      // 如果创建流失败，返回基于文件路径的简单哈希
      const hash = crypto.createHash('md5');
      hash.update(filePath);
      return hash.digest('hex');
    }
  }

  /**
   * 获取注册表统计信息
   * @returns 统计信息
   */
  public getRegistryStats(): RegistryStats {
    const files = Array.from(this.fileRegistry.values());
    
    return {
      totalFiles: files.length,
      validFiles: files.filter(f => f.isValid).length,
      invalidFiles: files.filter(f => !f.isValid).length,
      totalSize: files.reduce((sum, f) => sum + f.size, 0)
    };
  }

  /**
   * 清理无效文件记录
   */
  public async cleanupInvalidFiles(): Promise<void> {
    const toRemove: string[] = [];

    for (const [id, fileInfo] of this.fileRegistry.entries()) {
      try {
        if (!(await fs.pathExists(fileInfo.path))) {
          toRemove.push(id);
        }
      } catch {
        toRemove.push(id);
      }
    }

    toRemove.forEach(id => this.fileRegistry.delete(id));
  }

  /**
   * 更新配置
   * @param newConfig 新配置
   */
  public updateConfig(newConfig: FileOrganizationConfig): void {
    this.config = newConfig;
    this.validateConfig();
  }

  /**
   * 获取当前配置
   * @returns 当前配置
   */
  public getConfig(): FileOrganizationConfig {
    return { ...this.config };
  }
}
